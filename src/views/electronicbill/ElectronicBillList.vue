<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" @click="handleApply" preIcon="ant-design:plus-outlined"> 申请开票</a-button>
        <a-button type="primary" @click="handleBatchApply" preIcon="ant-design:upload-outlined" :disabled="!hasSelected"> 批量申请</a-button>
        <a-button @click="handleSync" preIcon="ant-design:sync-outlined"> 同步状态</a-button>
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction
          :actions="[
            {
              label: '详情',
              onClick: handleDetail.bind(null, record),
            },
            {
              label: '查看状态',
              onClick: handleQueryStatus.bind(null, record),
            },
            {
              label: '冲红',
              color: 'error',
              onClick: handleWriteOff.bind(null, record),
              ifShow: () => {
                return record.billStatus === '1' && record.isScarlet === '0';
              },
            },
            {
              label: '重新申请',
              color: 'warning',
              onClick: handleRetry.bind(null, record),
              ifShow: () => {
                return !record.billBatchCode;
              },
            },
            {
              label: '下载PDF',
              onClick: handleDownloadPdf.bind(null, record),
              ifShow: () => {
                return record.pictureUrl;
              },
            },
          ]"
        />
      </template>
    </BasicTable>
    
    <!-- 申请开票弹窗 -->
    <ElectronicBillModal @register="registerApplyModal" @success="handleSuccess" />
    
    <!-- 批量申请弹窗 -->
    <BasicModal @register="registerBatchModal" title="批量申请开票" width="800px" @ok="handleBatchConfirm">
      <div>
        <p>确认要为以下 {{ checkedKeys.length }} 个支付单申请电子票据吗？</p>
        <a-list size="small" bordered>
          <a-list-item v-for="key in checkedKeys.slice(0, 10)" :key="key">
            支付单ID: {{ key }}
          </a-list-item>
          <a-list-item v-if="checkedKeys.length > 10">
            还有 {{ checkedKeys.length - 10 }} 个支付单...
          </a-list-item>
        </a-list>
      </div>
    </BasicModal>
    
    <!-- 冲红弹窗 -->
    <BasicModal @register="registerWriteOffModal" title="电子票据冲红" width="600px" @ok="handleWriteOffConfirm">
      <BasicForm @register="registerWriteOffForm" />
    </BasicModal>
    
    <!-- 票据详情弹窗 -->
    <BasicModal @register="registerDetailModal" title="电子票据详情" width="1000px">
      <template #default>
        <div class="bill-detail">
          <a-descriptions :column="2" bordered>
            <a-descriptions-item label="业务流水号">{{ currentRecord?.busNo }}</a-descriptions-item>
            <a-descriptions-item label="电子票据号">{{ currentRecord?.electronicBillNo }}</a-descriptions-item>
            <a-descriptions-item label="票据代码">{{ currentRecord?.billBatchCode }}</a-descriptions-item>
            <a-descriptions-item label="校验码">{{ currentRecord?.randomCode }}</a-descriptions-item>
            <a-descriptions-item label="患者姓名">{{ currentRecord?.payer }}</a-descriptions-item>
            <a-descriptions-item label="开票金额">{{ currentRecord?.totalAmt }}</a-descriptions-item>
            <a-descriptions-item label="业务发生时间">{{ currentRecord?.busDateTime }}</a-descriptions-item>
            <a-descriptions-item label="开票时间">{{ currentRecord?.ivcDateTime }}</a-descriptions-item>
            <a-descriptions-item label="票据状态">
              <a-tag :color="getBillStatusColor(currentRecord?.billStatus)">
                {{ getBillStatusText(currentRecord?.billStatus) }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="是否冲红">
              <a-tag :color="currentRecord?.isScarlet === '1' ? 'red' : 'green'">
                {{ currentRecord?.isScarlet === '1' ? '已冲红' : '未冲红' }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="收费员">{{ currentRecord?.payee }}</a-descriptions-item>
            <a-descriptions-item label="开票点">{{ currentRecord?.placeCode }}</a-descriptions-item>
          </a-descriptions>
          
          <!-- 票据二维码 -->
          <div v-if="currentRecord?.billQrCode" class="qr-code-section">
            <h4>票据二维码</h4>
            <img :src="'data:image/png;base64,' + currentRecord.billQrCode" alt="票据二维码" style="max-width: 200px;" />
          </div>
          
          <!-- 操作按钮 -->
          <div class="action-buttons">
            <a-button v-if="currentRecord?.pictureUrl" type="primary" @click="viewBillOnline">
              在线查看票据
            </a-button>
            <a-button v-if="currentRecord?.pictureUrl" @click="downloadBillPdf">
              下载PDF
            </a-button>
          </div>
        </div>
      </template>
    </BasicModal>
  </div>
</template>

<script lang="ts" setup>
  import { reactive, computed, unref, ref } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { BasicModal, useModal } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { columns, searchFormSchema, writeOffFormSchema } from './electronicbill.data';
  import { 
    getElectronicBillList, 
    applyElectronicBill, 
    batchApplyElectronicBill,
    writeOffElectronicBill,
    queryBillStatus,
    retryApplyElectronicBill,
    syncBillStatus,
    getBillPdfUrl
  } from '/@/api/electronicbill/electronicbill';
  import ElectronicBillModal from './components/ElectronicBillModal.vue';

  const { createMessage } = useMessage();
  const { createConfirm } = useMessage();

  // 表格配置
  const [registerTable, { reload, getSelectRows }] = useTable({
    title: '电子票据列表',
    api: getElectronicBillList,
    rowKey: 'id',
    columns,
    formConfig: {
      labelWidth: 120,
      schemas: searchFormSchema,
      autoSubmitOnEnter: true,
    },
    useSearchForm: true,
    showTableSetting: true,
    bordered: true,
    actionColumn: {
      width: 200,
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
    },
  });

  // 弹窗配置
  const [registerApplyModal, { openModal: openApplyModal }] = useModal();
  const [registerBatchModal, { openModal: openBatchModal, closeModal: closeBatchModal }] = useModal();
  const [registerWriteOffModal, { openModal: openWriteOffModal, closeModal: closeWriteOffModal }] = useModal();
  const [registerDetailModal, { openModal: openDetailModal }] = useModal();

  // 冲红表单
  const [registerWriteOffForm, { getFieldsValue: getWriteOffValues, resetFields: resetWriteOffForm }] = useForm({
    labelWidth: 100,
    schemas: writeOffFormSchema,
    showActionButtonGroup: false,
  });

  // 选中行
  const checkedKeys = ref<Array<string>>([]);
  const currentRecord = ref<any>({});

  // 行选择
  const rowSelection = {
    type: 'checkbox',
    onChange: (selectedRowKeys: string[]) => {
      checkedKeys.value = selectedRowKeys;
    },
  };

  // 是否有选中行
  const hasSelected = computed(() => checkedKeys.value.length > 0);

  /**
   * 申请开票
   */
  function handleApply() {
    openApplyModal(true, {});
  }

  /**
   * 批量申请
   */
  function handleBatchApply() {
    if (checkedKeys.value.length === 0) {
      createMessage.warning('请先选择要申请开票的记录');
      return;
    }
    openBatchModal(true);
  }

  /**
   * 批量申请确认
   */
  async function handleBatchConfirm() {
    try {
      const result = await batchApplyElectronicBill(checkedKeys.value);
      const successCount = result.filter(item => item.success).length;
      
      createMessage.success(`批量申请完成，成功：${successCount}，失败：${result.length - successCount}`);
      closeBatchModal();
      checkedKeys.value = [];
      reload();
    } catch (error) {
      createMessage.error('批量申请失败');
    }
  }

  /**
   * 查看详情
   */
  function handleDetail(record: Recordable) {
    currentRecord.value = record;
    openDetailModal(true);
  }

  /**
   * 查询状态
   */
  async function handleQueryStatus(record: Recordable) {
    try {
      const result = await queryBillStatus(record.billId);
      if (result.success) {
        createMessage.success('查询成功');
        // 这里可以显示状态详情弹窗
        console.log('票据状态:', result.result);
      } else {
        createMessage.error(result.message || '查询失败');
      }
    } catch (error) {
      createMessage.error('查询状态失败');
    }
  }

  /**
   * 冲红
   */
  function handleWriteOff(record: Recordable) {
    currentRecord.value = record;
    resetWriteOffForm();
    openWriteOffModal(true);
  }

  /**
   * 冲红确认
   */
  async function handleWriteOffConfirm() {
    try {
      const values = await getWriteOffValues();
      const result = await writeOffElectronicBill(
        currentRecord.value.billId,
        values.reason,
        values.operator
      );
      
      if (result.success) {
        createMessage.success('冲红成功');
        closeWriteOffModal();
        reload();
      } else {
        createMessage.error(result.message || '冲红失败');
      }
    } catch (error) {
      createMessage.error('冲红失败');
    }
  }

  /**
   * 重新申请
   */
  function handleRetry(record: Recordable) {
    createConfirm({
      iconType: 'warning',
      title: '确认重新申请',
      content: '确定要重新申请电子票据吗？',
      onOk: async () => {
        try {
          const result = await retryApplyElectronicBill(record.billId);
          if (result.success) {
            createMessage.success('重新申请成功');
            reload();
          } else {
            createMessage.error(result.message || '重新申请失败');
          }
        } catch (error) {
          createMessage.error('重新申请失败');
        }
      },
    });
  }

  /**
   * 下载PDF
   */
  async function handleDownloadPdf(record: Recordable) {
    try {
      if (record.pictureUrl) {
        window.open(record.pictureUrl, '_blank');
      } else {
        const pdfUrl = await getBillPdfUrl(record.billId);
        if (pdfUrl) {
          window.open(pdfUrl, '_blank');
        } else {
          createMessage.warning('PDF地址不存在');
        }
      }
    } catch (error) {
      createMessage.error('获取PDF地址失败');
    }
  }

  /**
   * 同步状态
   */
  async function handleSync() {
    try {
      const result = await syncBillStatus();
      createMessage.success(`同步完成，成功同步${result}条记录`);
      reload();
    } catch (error) {
      createMessage.error('同步失败');
    }
  }

  /**
   * 成功回调
   */
  function handleSuccess() {
    reload();
  }

  /**
   * 在线查看票据
   */
  function viewBillOnline() {
    if (currentRecord.value?.pictureUrl) {
      window.open(currentRecord.value.pictureUrl, '_blank');
    }
  }

  /**
   * 下载票据PDF
   */
  function downloadBillPdf() {
    if (currentRecord.value?.pictureUrl) {
      // 创建下载链接
      const link = document.createElement('a');
      link.href = currentRecord.value.pictureUrl;
      link.download = `票据_${currentRecord.value.electronicBillNo}.pdf`;
      link.click();
    }
  }

  /**
   * 获取票据状态颜色
   */
  function getBillStatusColor(status: string) {
    const colorMap = {
      '1': 'green',
      '2': 'red',
    };
    return colorMap[status] || 'default';
  }

  /**
   * 获取票据状态文本
   */
  function getBillStatusText(status: string) {
    const textMap = {
      '1': '正常',
      '2': '作废',
    };
    return textMap[status] || '未知';
  }
</script>

<style lang="less" scoped>
  .bill-detail {
    .qr-code-section {
      margin-top: 20px;
      text-align: center;
      
      h4 {
        margin-bottom: 10px;
      }
    }
    
    .action-buttons {
      margin-top: 20px;
      text-align: center;
      
      .ant-btn {
        margin: 0 10px;
      }
    }
  }
</style>