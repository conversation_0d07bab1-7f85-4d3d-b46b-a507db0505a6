import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { h } from 'vue';
import { Tag } from 'ant-design-vue';

export const columns: BasicColumn[] = [
  {
    title: '业务流水号',
    dataIndex: 'busNo',
    width: 150,
  },
  {
    title: '电子票据号',
    dataIndex: 'electronicBillNo',
    width: 150,
  },
  {
    title: '票据代码',
    dataIndex: 'billBatchCode',
    width: 150,
  },
  {
    title: '患者姓名',
    dataIndex: 'payer',
    width: 100,
  },
  {
    title: '开票金额',
    dataIndex: 'totalAmt',
    width: 100,
    customRender: ({ text }) => {
      return `¥${text || 0}`;
    },
  },
  {
    title: '业务类型',
    dataIndex: 'busType',
    width: 80,
    customRender: ({ text }) => {
      const colorMap = {
        '01': 'blue',
        '02': 'green',
      };
      const textMap = {
        '01': '住院',
        '02': '门诊',
      };
      return h(Tag, { color: colorMap[text] }, () => textMap[text] || text);
    },
  },
  {
    title: '票据状态',
    dataIndex: 'billStatus',
    width: 80,
    customRender: ({ text }) => {
      const colorMap = {
        '1': 'green',
        '2': 'red',
      };
      const textMap = {
        '1': '正常',
        '2': '作废',
      };
      return h(Tag, { color: colorMap[text] }, () => textMap[text] || '未知');
    },
  },
  {
    title: '团检标志',
    dataIndex: 'teamFlag',
    width: 80,
    customRender: ({ text }) => {
      return h(Tag, { color: text === 1 ? 'orange' : 'default' }, () => text === 1 ? '团检' : '个检');
    },
  },
  {
    title: '是否冲红',
    dataIndex: 'isScarlet',
    width: 80,
    customRender: ({ text }) => {
      return h(Tag, { color: text === '1' ? 'red' : 'green' }, () => text === '1' ? '已冲红' : '未冲红');
    },
  },
  {
    title: '收费员',
    dataIndex: 'payee',
    width: 100,
  },
  {
    title: '业务发生时间',
    dataIndex: 'busDateTime',
    width: 150,
    customRender: ({ text }) => {
      if (!text) return '';
      // 格式化时间显示 yyyyMMddHHmmssSSS -> yyyy-MM-dd HH:mm:ss
      const year = text.substring(0, 4);
      const month = text.substring(4, 6);
      const day = text.substring(6, 8);
      const hour = text.substring(8, 10);
      const minute = text.substring(10, 12);
      const second = text.substring(12, 14);
      return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
    },
  },
  {
    title: '开票时间',
    dataIndex: 'ivcDateTime',
    width: 150,
    customRender: ({ text }) => {
      if (!text) return '';
      // 格式化时间显示
      const year = text.substring(0, 4);
      const month = text.substring(4, 6);
      const day = text.substring(6, 8);
      const hour = text.substring(8, 10);
      const minute = text.substring(10, 12);
      const second = text.substring(12, 14);
      return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
    },
  },
  {
    title: '同步状态',
    dataIndex: 'syncStatus',
    width: 80,
    customRender: ({ text }) => {
      const colorMap = {
        0: 'orange',
        1: 'green',
        2: 'red',
      };
      const textMap = {
        0: '未同步',
        1: '已同步',
        2: '同步失败',
      };
      return h(Tag, { color: colorMap[text] }, () => textMap[text] || '未知');
    },
  },
];

// 查询表单配置
export const searchFormSchema: FormSchema[] = [
  {
    field: 'busNo',
    label: '业务流水号',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'electronicBillNo',
    label: '电子票据号',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'billBatchCode',
    label: '票据代码',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'payer',
    label: '患者姓名',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'billStatus',
    label: '票据状态',
    component: 'Select',
    componentProps: {
      options: [
        { label: '全部', value: '' },
        { label: '正常', value: '1' },
        { label: '作废', value: '2' },
      ],
    },
    colProps: { span: 6 },
  },
  {
    field: 'teamFlag',
    label: '团检标志',
    component: 'Select',
    componentProps: {
      options: [
        { label: '全部', value: '' },
        { label: '个检', value: 0 },
        { label: '团检', value: 1 },
      ],
    },
    colProps: { span: 6 },
  },
  {
    field: '[startDate, endDate]',
    label: '时间范围',
    component: 'RangePicker',
    componentProps: {
      format: 'YYYY-MM-DD',
      placeholder: ['开始日期', '结束日期'],
    },
    colProps: { span: 12 },
  },
];

// 申请开票表单配置
export const applyFormSchema: FormSchema[] = [
  {
    field: 'billId',
    label: '支付单ID',
    component: 'Input',
    required: true,
    helpMessage: '请输入要申请开票的支付单ID',
  },
  {
    field: 'busType',
    label: '业务类型',
    component: 'Select',
    componentProps: {
      options: [
        { label: '门诊', value: '02' },
        { label: '住院', value: '01' },
      ],
    },
    defaultValue: '02',
    required: true,
  },
  {
    field: 'remark',
    label: '备注',
    component: 'InputTextArea',
    componentProps: {
      placeholder: '请输入备注信息',
      rows: 3,
    },
  },
];

// 冲红表单配置
export const writeOffFormSchema: FormSchema[] = [
  {
    field: 'reason',
    label: '冲红原因',
    component: 'InputTextArea',
    required: true,
    componentProps: {
      placeholder: '请输入冲红原因',
      rows: 3,
      maxlength: 200,
      showCount: true,
    },
    rules: [
      {
        required: true,
        message: '请输入冲红原因',
      },
      {
        min: 5,
        message: '冲红原因至少5个字符',
      },
    ],
  },
  {
    field: 'operator',
    label: '操作人',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入操作人姓名',
    },
    rules: [
      {
        required: true,
        message: '请输入操作人',
      },
    ],
  },
];