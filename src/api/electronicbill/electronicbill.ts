import { defHttp } from '/@/utils/http/axios';
import { useMessage } from '/@/hooks/web/useMessage';

const { createMessage } = useMessage();

enum Api {
  list = '/electronicbill/bill/list',
  apply = '/electronicbill/bill/apply',
  batchApply = '/electronicbill/bill/batchApply',
  writeOff = '/electronicbill/bill/writeOff',
  queryStatus = '/electronicbill/bill/queryStatus',
  queryStatusByBillNo = '/electronicbill/bill/queryStatusByBillNo',
  retry = '/electronicbill/bill/retry',
  syncStatus = '/electronicbill/bill/syncStatus',
  getPdfUrl = '/electronicbill/bill/getPdfUrl',
  getQrCode = '/electronicbill/bill/getQrCode',
  statistics = '/electronicbill/bill/statistics',
}

/**
 * 获取电子票据列表
 */
export const getElectronicBillList = (params?: any) =>
  defHttp.get<any>({ url: Api.list, params });

/**
 * 申请电子票据
 */
export const applyElectronicBill = (billId: string) =>
  defHttp.post<any>({
    url: Api.apply,
    params: { billId },
  });

/**
 * 批量申请电子票据
 */
export const batchApplyElectronicBill = (billIds: string[]) =>
  defHttp.post<any>({
    url: Api.batchApply,
    data: billIds,
  });

/**
 * 电子票据冲红
 */
export const writeOffElectronicBill = (billId: string, reason: string, operator: string) =>
  defHttp.post<any>({
    url: Api.writeOff,
    params: { billId, reason, operator },
  });

/**
 * 查询票据状态
 */
export const queryBillStatus = (billId: string) =>
  defHttp.get<any>({
    url: Api.queryStatus,
    params: { billId },
  });

/**
 * 根据票据代码和号码查询状态
 */
export const queryBillStatusByBillNo = (billBatchCode: string, billNo: string) =>
  defHttp.get<any>({
    url: Api.queryStatusByBillNo,
    params: { billBatchCode, billNo },
  });

/**
 * 重新申请电子票据
 */
export const retryApplyElectronicBill = (billId: string) =>
  defHttp.post<any>({
    url: Api.retry,
    params: { billId },
  });

/**
 * 同步票据状态
 */
export const syncBillStatus = () =>
  defHttp.post<any>({ url: Api.syncStatus });

/**
 * 获取票据PDF下载地址
 */
export const getBillPdfUrl = (billId: string) =>
  defHttp.get<string>({
    url: Api.getPdfUrl,
    params: { billId },
  });

/**
 * 获取票据二维码
 */
export const getBillQrCode = (billId: string) =>
  defHttp.get<string>({
    url: Api.getQrCode,
    params: { billId },
  });

/**
 * 获取统计信息
 */
export const getElectronicBillStatistics = (params?: any) =>
  defHttp.get<any>({
    url: Api.statistics,
    params,
  });

/**
 * 票据相关工具函数
 */
export const ElectronicBillUtils = {
  /**
   * 格式化票据状态
   */
  formatBillStatus(status: string): { text: string; color: string } {
    const statusMap = {
      '1': { text: '正常', color: 'green' },
      '2': { text: '作废', color: 'red' },
    };
    return statusMap[status] || { text: '未知', color: 'default' };
  },

  /**
   * 格式化业务类型
   */
  formatBusType(busType: string): { text: string; color: string } {
    const typeMap = {
      '01': { text: '住院', color: 'blue' },
      '02': { text: '门诊', color: 'green' },
      '03': { text: '挂号', color: 'orange' },
      '04': { text: '体检', color: 'purple' },
    };
    return typeMap[busType] || { text: '未知', color: 'default' };
  },

  /**
   * 格式化时间
   */
  formatDateTime(dateTimeStr: string): string {
    if (!dateTimeStr || dateTimeStr.length < 14) return '';
    
    const year = dateTimeStr.substring(0, 4);
    const month = dateTimeStr.substring(4, 6);
    const day = dateTimeStr.substring(6, 8);
    const hour = dateTimeStr.substring(8, 10);
    const minute = dateTimeStr.substring(10, 12);
    const second = dateTimeStr.substring(12, 14);
    
    return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
  },

  /**
   * 验证票据号格式
   */
  validateBillNo(billNo: string): boolean {
    // 这里可以根据实际票据号规则进行验证
    return /^[A-Z0-9]{10,20}$/.test(billNo);
  },

  /**
   * 下载票据文件
   */
  async downloadBillFile(billId: string, fileName?: string) {
    try {
      const pdfUrl = await getBillPdfUrl(billId);
      if (pdfUrl) {
        const link = document.createElement('a');
        link.href = pdfUrl;
        link.download = fileName || `电子票据_${billId}.pdf`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        createMessage.success('下载成功');
      } else {
        createMessage.warning('PDF地址不存在');
      }
    } catch (error) {
      createMessage.error('下载失败');
    }
  },

  /**
   * 复制票据信息到剪贴板
   */
  async copyBillInfo(record: any) {
    const info = `
电子票据信息：
业务流水号：${record.busNo || ''}
电子票据号：${record.electronicBillNo || ''}
票据代码：${record.billBatchCode || ''}
患者姓名：${record.payer || ''}
开票金额：¥${record.totalAmt || 0}
开票时间：${this.formatDateTime(record.ivcDateTime || '')}
    `.trim();

    try {
      await navigator.clipboard.writeText(info);
      createMessage.success('票据信息已复制到剪贴板');
    } catch (error) {
      createMessage.error('复制失败');
    }
  },
};