import { defHttp } from '@/utils/http/axios'

const Api = {
  // 基础操作
  list: '/electronicbill/bill/list',
  add: '/electronicbill/bill/add',
  edit: '/electronicbill/bill/edit',
  delete: '/electronicbill/bill/delete',
  deleteBatch: '/electronicbill/bill/deleteBatch',
  queryById: '/electronicbill/bill/queryById',
  
  // 电子票据操作
  apply: '/electronicbill/bill/apply',
  batchApply: '/electronicbill/bill/batchApply',
  writeOff: '/electronicbill/bill/writeOff',
  queryStatus: '/electronicbill/bill/queryStatus',
  queryStatusByBillNo: '/electronicbill/bill/queryStatusByBillNo',
  retry: '/electronicbill/bill/retry',
  syncStatus: '/electronicbill/bill/syncStatus',
  getPdfUrl: '/electronicbill/bill/getPdfUrl',
  getQrCode: '/electronicbill/bill/getQrCode',
  statistics: '/electronicbill/bill/statistics',
  
  // 新增的数据验证和查询接口
  getBillsByDate: '/electronicbill/bill/getBillsByDate',
  checkTotalData: '/electronicbill/bill/checkTotalData',
  checkWriteOffData: '/electronicbill/bill/checkWriteOffData',
  getBillsByBusDate: '/electronicbill/bill/getBillsByBusDate',
  checkTotalDataByIvcDate: '/electronicbill/bill/checkTotalDataByIvcDate',
  checkWriteOffDataByIvcDate: '/electronicbill/bill/checkWriteOffDataByIvcDate'
}

/**
 * 分页列表查询
 */
export const getElectronicBillList = (params) => defHttp.get({ url: Api.list, params })

/**
 * 添加电子票据
 */
export const addElectronicBill = (data) => defHttp.post({ url: Api.add, data })

/**
 * 编辑电子票据
 */
export const editElectronicBill = (data) => defHttp.put({ url: Api.edit, data })

/**
 * 删除电子票据
 */
export const deleteElectronicBill = (params) => defHttp.delete({ url: Api.delete, params })

/**
 * 批量删除电子票据
 */
export const deleteBatchElectronicBill = (params) => defHttp.delete({ url: Api.deleteBatch, params })

/**
 * 根据ID查询电子票据
 */
export const getElectronicBillById = (params) => defHttp.get({ url: Api.queryById, params })

/**
 * 申请电子票据
 */
export const applyElectronicBill = (params) => defHttp.post({ url: Api.apply, params })

/**
 * 批量申请电子票据
 */
export const batchApplyElectronicBill = (data) => defHttp.post({ url: Api.batchApply, data })

/**
 * 电子票据冲红
 */
export const writeOffElectronicBill = (params) => defHttp.post({ url: Api.writeOff, params })

/**
 * 查询票据状态
 */
export const queryBillStatus = (params) => defHttp.get({ url: Api.queryStatus, params })

/**
 * 根据票据代码和号码查询状态
 */
export const queryBillStatusByBillNo = (params) => defHttp.get({ url: Api.queryStatusByBillNo, params })

/**
 * 重新申请电子票据
 */
export const retryApplyElectronicBill = (params) => defHttp.post({ url: Api.retry, params })

/**
 * 同步票据状态
 */
export const syncBillStatus = () => defHttp.post({ url: Api.syncStatus })

/**
 * 获取票据PDF下载地址
 */
export const getBillPdfUrl = (params) => defHttp.get({ url: Api.getPdfUrl, params })

/**
 * 获取票据二维码
 */
export const getBillQrCode = (params) => defHttp.get({ url: Api.getQrCode, params })

/**
 * 获取统计信息
 */
export const getStatistics = (params) => defHttp.get({ url: Api.statistics, params })

// ===== 新增的数据验证和查询接口 =====

/**
 * 根据开票日期获取开票信息
 */
export const getBillsByDate = (params) => defHttp.get({ url: Api.getBillsByDate, params })

/**
 * 总笔数核对
 */
export const checkTotalData = (params) => defHttp.post({ url: Api.checkTotalData, params })

/**
 * 冲红数据核对
 */
export const checkWriteOffData = (params) => defHttp.post({ url: Api.checkWriteOffData, params })

/**
 * 根据业务日期获取开票信息
 */
export const getBillsByBusDate = (params) => defHttp.get({ url: Api.getBillsByBusDate, params })

/**
 * 按开票日期总笔数核对
 */
export const checkTotalDataByIvcDate = (params) => defHttp.post({ url: Api.checkTotalDataByIvcDate, params })

/**
 * 按开票日期冲红数据核对
 */
export const checkWriteOffDataByIvcDate = (params) => defHttp.post({ url: Api.checkWriteOffDataByIvcDate, params })