server:
  port: 8090
  tomcat:
    max-swallow-size: -1
  error:
    include-exception: true
    include-stacktrace: ALWAYS
    include-message: ALWAYS
  servlet:
    context-path: /jeecgboot

spring:
  # flyway配置
  flyway:
    # 是否启用flyway
    enabled: false
    # 编码格式，默认UTF-8
    encoding: UTF-8
    # 迁移sql脚本文件存放路径，官方默认db/migration
    locations: classpath:flyway/sql/mysql
    # 迁移sql脚本文件名称的前缀，默认V
    sql-migration-prefix: V
    # 迁移sql脚本文件名称的分隔符，默认2个下划线__
    sql-migration-separator: __
    # 避免带${}sql执行失败
    placeholder-prefix: '#('
    placeholder-suffix: )
    # 迁移sql脚本文件名称的后缀
    sql-migration-suffixes: .sql
    # 迁移时是否进行校验，默认true
    validate-on-migrate: true
    # 当迁移发现数据库非空且存在没有元数据的表时，自动执行基准迁移，新建schema_version表
    baseline-on-migrate: true
    # 是否关闭要清除已有库下的表功能,生产环境必须为true,否则会删库，非常重要！！！
    clean-disabled: true
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
  jms:
    listener:
      back-off-time: 5000
      max-retry-attempts: 30
  # activemq
  activemq:
    broker-url: tcp://localhost:61616
    in-memory: false
    packages:
      trust-all: true
    password: manager

# 支付网关配置
payment:
  gateway:
    # 默认支付提供商: jeepay | innerMongolia
    default-provider: jeepay
    # 是否启用降级机制
    fallback-enabled: true
    # 降级支付提供商
    fallback-provider: jeepay

# 内蒙古医院统一支付平台配置
inner:
  mongolia:
    pay:
      # 网关地址
      gateway:
        url: http://test-cn.your-api-server.com/realpay/gateway
      # 应用ID
      app:
        id: your_app_id_here
      # RSA私钥(用于签名)
      private:
        key: your_private_key_here
      # RSA公钥(用于验签)
      public:
        key: your_public_key_here
      # 支付成功回调地址
      notify:
        url: http://your-domain.com/jeecgboot/fee/innerMongolia/notify/paySuccess
    user: system
    pool:
      enabled: true
      max-connections: 10
      idle-timeout: 30000  # 空闲的连接过期时间，默认为30秒
  mail:
    host: smtp.163.com
    username: <EMAIL>
    password: ??
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
  ## quartz定时任务,采用数据库方式
  quartz:
    job-store-type: jdbc
    initialize-schema: embedded
    #定时任务启动开关，true-开  false-关
    auto-startup: true
    #延迟1秒启动定时任务
    startup-delay: 1s
    #启动时更新己存在的Job
    overwrite-existing-jobs: true
    properties:
      org:
        quartz:
          scheduler:
            instanceName: MyScheduler
            instanceId: AUTO
          jobStore:
            class: org.springframework.scheduling.quartz.LocalDataSourceJobStore
            driverDelegateClass: org.quartz.impl.jdbcjobstore.StdJDBCDelegate
            tablePrefix: QRTZ_
            isClustered: true
            misfireThreshold: 12000
            clusterCheckinInterval: 15000
          threadPool:
            class: org.quartz.simpl.SimpleThreadPool
            threadCount: 10
            threadPriority: 5
            threadsInheritContextClassLoaderOfInitializingThread: true
  #json 时间戳统一转换
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  jpa:
    open-in-view: false
  aop:
    proxy-target-class: true
  #配置freemarker
  freemarker:
    # 设置模板后缀名
    suffix: .ftl
    # 设置文档类型
    content-type: text/html
    # 设置页面编码格式
    charset: UTF-8
    # 设置页面缓存
    cache: false
    prefer-file-system-access: false
    # 设置ftl文件路径
    template-loader-path:
      - classpath:/templates
    template_update_delay: 0
  # 设置静态文件路径，js,css等
  mvc:
    static-path-pattern: /**
    #Spring Boot 2.6+后映射匹配的默认策略已从AntPathMatcher更改为PathPatternParser,需要手动指定为ant-path-matcher
    pathmatch:
      matching-strategy: ant_path_matcher
  resource:
    static-locations: classpath:/static/,classpath:/public/
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  datasource:
    druid:
      stat-view-servlet:
        enabled: true
        loginUsername: admin
        loginPassword: 123456
        allow:
      web-stat-filter:
        enabled: true
    dynamic:
      druid: # 全局druid参数，绝大部分值和默认保持一致。(现已支持的参数如下,不清楚含义不要乱设置)
        # 连接池的配置信息
        # 初始化大小，最小，最大
        initial-size: 5
        min-idle: 5
        maxActive: 1000
        # 配置获取连接等待超时的时间
        maxWait: 60000
        # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
        timeBetweenEvictionRunsMillis: 60000
        # 配置一个连接在池中最小生存的时间，单位是毫秒
        minEvictableIdleTimeMillis: 300000
        validationQuery: SELECT 1
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        # 打开PSCache，并且指定每个连接上PSCache的大小
        poolPreparedStatements: true
        maxPoolPreparedStatementPerConnectionSize: 20
        # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
        filters: stat,slf4j
        # 打开mergeSql功能；慢SQL记录
        stat:
          merge-sql: true
          slow-sql-millis: 5000
      datasource:
        master:
          #          url: **********************************************************************************************************************************************************************************
          #          username: root
          #          password: root
          #          driver-class-name: com.mysql.cj.jdbc.Driver
          #url: **************************************************************************************************************************************************************************************************************************
          #username: pes
          #password: Pes123!@#
          #driver-class-name: com.mysql.cj.jdbc.Driver
          url: **********************************************************************************************************************************************************************************
          username: pes
          password: Pes123!@#
          driver-class-name: com.mysql.cj.jdbc.Driver
          #url: *********************************************************************************************************************************************************************************************************************
          #username: pes
          #password: Pes123!@#
          #driver-class-name: com.mysql.cj.jdbc.Driver
          #          url: *****************************************************************************************************************************************************************************
          #          username: root
          #          password: root
          #          driver-class-name: com.mysql.cj.jdbc.Driver
          # 多数据源配置
          #multi-datasource1:
          #url: *********************************************************************************************************************************************************************************************************************************
          #username: root
          #password: root
          #driver-class-name: com.mysql.cj.jdbc.Driver
  #redis 配置
  redis:
    database: 2
    host: 127.0.0.1
    port: 6379
    password: ''
#mybatis plus 设置
mybatis-plus:
  mapper-locations: classpath*:org/jeecg/modules/**/xml/*Mapper.xml
  global-config:
    # 关闭MP3.0自带的banner
    banner: false
    db-config:
      #主键类型  0:"数据库ID自增",1:"该类型为未设置主键类型", 2:"用户输入ID",3:"全局唯一ID (数字类型唯一ID)", 4:"全局唯一ID UUID",5:"字符串全局唯一ID (idWorker 的字符串表示)";
      #id-type: ASSIGN_ID
      # 默认数据库表下划线命名
      table-underline: true
      logic-delete-value: 1
      logic-not-delete-value: 0
      version-field-name: version
  configuration:
    # 这个配置会将执行的sql打印出来，在开发或测试的时候可以用
    #log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    # 返回类型为Map,显示null对应的字段
    call-setters-on-nulls: true
#jeecg专用配置
minidao:
  base-package: org.jeecg.modules.jmreport.*,org.jeecg.modules.drag.*
jeecg:
  encrypt:
    algorithm: SM3
  # AI模型
  ai-chat:
    enabled: true
    apiKey: sk-ca9b749b166a4a39b158b9c634750504
    model: deepseek-chat
    apiHost: https://api.deepseek.com
    timeout: 60
  idgenerator:
    workerId: 1
    datacenterId: 1
  # 平台上线安全配置
  firewall:
    # 数据源安全 (开启后，Online报表和图表的数据源为必填)
    dataSourceSafe: false
    # 低代码模式（dev:开发模式，prod:发布模式——关闭所有在线开发配置能力）
    lowCodeMode: dev
  # 授权管理配置
  license:
    # 是否启用授权验证
    enabled: false
    # 授权文件存储路径
    licensePath: license/license.lic
    # 公钥库路径
    publicKeyPath: license/publicCerts.keystore
  # 签名密钥串(前后端要一致，正式发布请自行修改)
  signatureSecret: dd05f1c54d63749eda95f9fa6d49v442a
  #签名拦截接口
  signUrls: /sys/dict/getDictItems/*,/sys/dict/loadDict/*,/sys/dict/loadDictOrderByValue/*,/sys/dict/loadDictItem/*,/sys/dict/loadTreeData,/sys/api/queryTableDictItemsByCode,/sys/api/queryFilterTableDictInfo,/sys/api/queryTableDictByKeys,/sys/api/translateDictFromTable,/sys/api/translateDictFromTableByKeys
  # 本地：local、Minio：minio、阿里云：alioss
  uploadType: minio
  # 前端访问地址
  domainUrl:
    pc: http://localhost:3100
    app: http://localhost:8051
  path:
    #文件上传根目录 设置
    upload: /opt/upFiles
    #webapp文件路径
    webapp: /opt/webapp
  shiro:
    excludeUrls: /test/jeecgDemo/demo3,/test/jeecgDemo/redisDemo/**,/jmreport/bigscreen2/**,/services/**,/fee/anon/paytestNotify/payOrder
  #阿里云oss存储和大鱼短信秘钥配置
  oss:
    accessKey: ??
    secretKey: ??
    endpoint: oss-cn-beijing.aliyuncs.com
    bucketName: jeecgdev
  # ElasticSearch 6设置
  elasticsearch:
    cluster-name: jeecg-ES
    cluster-nodes: 127.0.0.1:9200
    check-enabled: false
  # 在线预览文件服务器地址配置
  file-view-domain: http://fileview.jeecg.com
  # minio文件上传
  minio:
    minio_url: http://localhost:9000
    minio_name: cIHSfxmIFVjpplRUQ80t
    minio_pass: eJbm7pTyMWoylccTeuCJqFrz8A5NOmgR0YLzr6yW
    bucketName: images
  #大屏报表参数设置
  jmreport:
    #多租户模式，默认值为空(created:按照创建人隔离、tenant:按照租户隔离) (v1.6.2+ 新增)
    saasMode:
    # 平台上线安全配置(v1.6.2+ 新增)
    firewall:
      # 数据源安全 (开启后，不允许使用平台数据源、SQL解析加签并且不允许查询数据库)
      dataSourceSafe: false
      # 低代码开发模式（dev:开发模式，prod:发布模式—关闭在线报表设计功能，分配角色admin、lowdeveloper可以放开限制）
      lowCodeMode: dev
  #xxl-job配置
  xxljob:
    enabled: false
    adminAddresses: http://127.0.0.1:9080/xxl-job-admin
    appname: ${spring.application.name}
    accessToken: ''
    address: 127.0.0.1:30007
    ip: 127.0.0.1
    port: 30007
    logPath: logs/jeecg/job/jobhandler/
    logRetentionDays: 30
  #分布式锁配置
  redisson:
    address: 127.0.0.1:6379
    password:
    type: STANDALONE
    enabled: true
#cas单点登录
cas:
  prefixUrl: http://cas.example.org:8443/cas
#swagger
knife4j:
  #开启增强配置
  enable: true
  #开启生产环境屏蔽
  production: false
  basic:
    enable: false
    username: jeecg
    password: jeecg1314
#第三方登录
justauth:
  enabled: true
  type:
    GITHUB:
      client-id: ??
      client-secret: ??
      redirect-uri: http://sso.test.com:8080/jeecg-boot/sys/thirdLogin/github/callback
    WECHAT_ENTERPRISE:
      client-id: ??
      client-secret: ??
      redirect-uri: http://sso.test.com:8080/jeecg-boot/sys/thirdLogin/wechat_enterprise/callback
      agent-id: ??
    DINGTALK:
      client-id: ??
      client-secret: ??
      redirect-uri: http://sso.test.com:8080/jeecg-boot/sys/thirdLogin/dingtalk/callback
    WECHAT_OPEN:
      client-id: ??
      client-secret: ??
      redirect-uri: http://sso.test.com:8080/jeecg-boot/sys/thirdLogin/wechat_open/callback
  cache:
    type: default
    prefix: 'demo::'
    timeout: 1h
apiAuth:
  ai:
    apiKey: 1
    apiSecret: 1
biz:
  sms:
    md:
      sn: DXX-DSN-010-00016
      account: 618098
      extno: 10690
      password: elel3PhY8Kv8j
      signCode: 【利康管家】
      serviceURL: http://8.142.148.197:7862/sms
  const:
    summary:
      normalResult: '未见明显异常'
  lucene:
    path: /opt/lucene
hanlp:
  enablePorterStemming: false
  enableStopwordFiltering: true
  enableAllNamedEntityRecognize: true
  enableNumberQuantifierRecognize: true
  enableCustomDictionary: true
  enableTranslatedNameRecognize: true
  enableJapaneseNameRecognize: false
  enableOrganizationRecognize: false
  enablePlaceRecognize: false
  enableSpeechTagging: false
  enableOffset: true
camunda:
  bpm:
    database:
      type: mysql
      schema-update: true
    identity:
      enabled: true
    metrics:
      enabled: false
    history-level: full
    id-generator: simple
cxf:
  jaxws-client:
    default-client:
      disable-schema-validation: true
      schema-validation-enabled: false
proxy:
  enabled: false
  host: localhost
  port: 808
  username:
  password:
selenium:
  chromePath: '/Users/<USER>/chrome/chrome-mac-arm64/Google Chrome for Testing.app/Contents/MacOS/Google Chrome for Testing'
  chromeDriverPath: /Users/<USER>/chrome/chromedriver-mac-arm64/chromedriver
ai:
  baichuan:
    host: https://api.baichuan-ai.com
  deepSeek:
    host: https://api.deepseek.com
    apiSecret: ***********************************
  dashscope:
    baseUrl: https://dashscope.aliyuncs.com
    chatCompletionsEndpoint: /compatible-mode/v1/chat/completions
    apiSecret: ***********************************

# 在适当位置添加以下配置
socketio:
  host: 0.0.0.0
  port: 9092
  bossCount: 1
  workCount: 100
  allowCustomRequests: true
  upgradeTimeout: 10000
  pingTimeout: 60000
  pingInterval: 25000

# Apache Calcite配置文件
calcite:
  # 是否启用Calcite
  enabled: true

# JVM参数建议（在启动脚本中配置）
# -Xmx4g -Xms4g
# -XX:+UseG1GC
# -XX:MaxGCPauseMillis=200
# -XX:+UseStringDeduplication

  # 流式处理配置
  streaming:
    enabled: true
    memory-limit: 2GB
    # 针对亿级数据优化批次大小
    batch-size: 5000
    spill-to-disk: true
    temp-directory: ./temp/calcite
    # 降低内存阈值，更积极的内存管理
    memory-threshold: 75.0
    # 启用压缩以节省内存
    enable-compression: true
    # 流式处理缓冲区大小
    buffer-size: 8192
    # 最大并发流数量
    max-concurrent-streams: 3
    # 启用增量处理
    enable-incremental: true
    # 数据分片大小（行数）
    chunk-size: 50000

  # 查询优化配置
  optimization:
    # 启用所有优化规则
    enable-join-reorder: true
    enable-predicate-pushdown: true
    enable-projection-pushdown: true
    enable-limit-pushdown: true
    enable-aggregate-pushdown: true
    enable-sort-pushdown: true

    # 基于成本的优化
    cost-based-optimization: true
    enable-statistics: true

    # 超时配置 - 针对复杂查询
    query-timeout: 600
    parse-timeout: 60
    optimize-timeout: 120

    # 缓存配置 - 针对重复查询优化
    enable-query-cache: true
    cache-size: 200
    cache-expire-minutes: 120
    enable-plan-cache: true
    plan-cache-size: 50

    # 并行处理配置
    enable-parallel-execution: true
    max-parallel-degree: 4
    parallel-threshold: 100000

    # 内存管理
    max-memory-per-query: 512MB
    spill-threshold: 256MB

    # 统计信息配置
    auto-update-statistics: true
    statistics-sample-rate: 0.1

  # MySQL连接配置
  mysql:
    # 使用动态数据源的主库配置
    url: **********************************************************************************************************************************************************************************&rewriteBatchedStatements=true&useServerPrepStmts=true&cachePrepStmts=true&prepStmtCacheSize=250&prepStmtCacheSqlLimit=2048&useLocalSessionState=true&elideSetAutoCommits=true&alwaysSendSetIsolation=false&enableQueryTimeouts=false&connectionAttributes=none&verifyServerCertificate=false&useInformationSchema=true&maintainTimeStats=false&generateSimpleParameterMetadata=true&useReadAheadInput=false&useUnbufferedInput=false&useStreamLengthsInPrepStmts=true&useJDBCCompliantTimezoneShift=true&useLegacyDatetimeCode=false&sendFractionalSeconds=false&treatUtilDateAsTimestamp=false&useFastDateParsing=true&useFastIntParsing=true&dumpQueriesOnException=false&useDirectRowUnpack=true&cacheCallableStmts=true&autoClosePStmtStreams=true
    username: pes
    password: Pes123!@#
    driver: com.mysql.cj.jdbc.Driver

    # 连接池配置 - 针对大数据量优化
    max-pool-size: 20
    min-pool-size: 5
    initial-size: 5

    # 超时配置 - 针对复杂查询优化
    connection-timeout: 60
    idle-timeout: 600
    max-lifetime: 1800
    validation-timeout: 5

    # 查询优化配置
    query-timeout: 600
    socket-timeout: 300

    # 批处理优化
    default-fetch-size: 1000
    use-server-prep-stmts: true
    cache-prep-stmts: true
    prep-stmt-cache-size: 250
    prep-stmt-cache-sql-limit: 2048

    # 性能优化参数
    use-local-session-state: true
    rewrite-batched-statements: true
    cache-result-set-metadata: true
    cache-server-configuration: true
    elide-set-auto-commits: true
    maintain-time-stats: false

    # 大数据量处理优化
    use-stream-lengths-in-prep-stmts: true
    use-cursor-fetch: true
    default-row-prefetch: 100

    # 内存优化
    use-blob-to-store-utf8-outside-bmp: false
    use-direct-row-unpack: true
    large-row-size-threshold: 2048

# JVM内存配置建议
# -Xmx4g -Xms4g
# -XX:+UseG1GC
# -XX:MaxGCPauseMillis=100
# -XX:+UseStringDeduplication
# -XX:NewRatio=1


# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,calcite
  endpoint:
    calcite:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true
#Mybatis输出sql日志
logging:
  level:
    org.flywaydb: debug
    org.jeecg.modules.system.mapper: info
    org.apache:
      cxf: DEBUG
      cxf.phase.PhaseInterceptorChain: DEBUG
    org.apache.calcite: INFO
    org.jeecg.modules.calcite: DEBUG