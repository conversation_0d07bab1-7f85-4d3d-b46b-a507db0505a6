package org.jeecg.modules.fee.gateway.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.fee.entity.PayConfig;
import org.jeecg.modules.fee.gateway.IPaymentGateway;
import org.jeecg.modules.fee.gateway.dto.*;
import org.jeecg.modules.fee.gateway.enums.PaymentGatewayType;
import org.jeecg.modules.fee.gateway.exception.PaymentGatewayException;
import org.jeecg.modules.fee.gateway.factory.PaymentGatewayFactory;
import org.jeecg.modules.fee.gateway.service.IPaymentGatewayService;
import org.jeecg.modules.fee.service.IFeePayRecordService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class PaymentGatewayServiceImpl implements IPaymentGatewayService {
    
    @Resource
    private PaymentGatewayFactory gatewayFactory;
    
    @Resource
    private IFeePayRecordService feePayRecordService;
    
    @Override
    public PaymentResponse createPayment(PaymentRequest request) {
        return createPayment(request, null);
    }
    
    @Override
    public PaymentResponse createPayment(PaymentRequest request, PaymentGatewayType gatewayType) {
        try {
            IPaymentGateway gateway = getGateway(gatewayType);
            
            log.info("创建支付订单: gateway={}, orderNo={}, amount={}", 
                gateway.getGatewayName(), request.getOrderNo(), request.getAmount());
            
            PaymentResponse response = gateway.createPayment(request);
            
            log.info("支付订单创建结果: gateway={}, orderNo={}, success={}, code={}", 
                gateway.getGatewayName(), request.getOrderNo(), response.isSuccess(), response.getCode());
            
            return response;
        } catch (Exception e) {
            log.error("创建支付订单失败: orderNo={}", request.getOrderNo(), e);
            return PaymentResponse.error("GATEWAY_ERROR", "创建支付订单失败: " + e.getMessage());
        }
    }
    
    @Override
    public PaymentResponse createQrCodePayment(PaymentRequest request) {
        return createQrCodePayment(request, null);
    }
    
    @Override
    public PaymentResponse createQrCodePayment(PaymentRequest request, PaymentGatewayType gatewayType) {
        try {
            IPaymentGateway gateway = getGateway(gatewayType);
            
            log.info("创建扫码支付: gateway={}, orderNo={}, amount={}", 
                gateway.getGatewayName(), request.getOrderNo(), request.getAmount());
            
            PaymentResponse response = gateway.createQrCodePayment(request);
            
            log.info("扫码支付创建结果: gateway={}, orderNo={}, success={}, code={}", 
                gateway.getGatewayName(), request.getOrderNo(), response.isSuccess(), response.getCode());
            
            return response;
        } catch (Exception e) {
            log.error("创建扫码支付失败: orderNo={}", request.getOrderNo(), e);
            return PaymentResponse.error("GATEWAY_ERROR", "创建扫码支付失败: " + e.getMessage());
        }
    }
    
    @Override
    public PaymentResponse createBarcodePayment(PaymentRequest request) {
        return createBarcodePayment(request, null);
    }
    
    @Override
    public PaymentResponse createBarcodePayment(PaymentRequest request, PaymentGatewayType gatewayType) {
        try {
            IPaymentGateway gateway = getGateway(gatewayType);
            
            log.info("创建条码支付: gateway={}, orderNo={}, amount={}, authCode={}", 
                gateway.getGatewayName(), request.getOrderNo(), request.getAmount(), 
                maskAuthCode(request.getAuthCode()));
            
            PaymentResponse response = gateway.createBarcodePayment(request);
            
            log.info("条码支付创建结果: gateway={}, orderNo={}, success={}, code={}", 
                gateway.getGatewayName(), request.getOrderNo(), response.isSuccess(), response.getCode());
            
            return response;
        } catch (Exception e) {
            log.error("创建条码支付失败: orderNo={}", request.getOrderNo(), e);
            return PaymentResponse.error("GATEWAY_ERROR", "创建条码支付失败: " + e.getMessage());
        }
    }
    
    @Override
    public QueryResponse queryPayment(QueryRequest request) {
        return queryPayment(request, null);
    }
    
    @Override
    public QueryResponse queryPayment(QueryRequest request, PaymentGatewayType gatewayType) {
        try {
            IPaymentGateway gateway = getGateway(gatewayType);
            
            log.info("查询支付订单: gateway={}, orderNo={}", 
                gateway.getGatewayName(), request.getOrderNo());
            
            QueryResponse response = gateway.queryPayment(request);
            
            log.info("支付订单查询结果: gateway={}, orderNo={}, success={}, status={}", 
                gateway.getGatewayName(), request.getOrderNo(), response.isSuccess(), response.getStatus());
            
            return response;
        } catch (Exception e) {
            log.error("查询支付订单失败: orderNo={}", request.getOrderNo(), e);
            return QueryResponse.error("GATEWAY_ERROR", "查询支付订单失败: " + e.getMessage());
        }
    }
    
    @Override
    public RefundResponse refundPayment(RefundRequest request) {
        return refundPayment(request, null);
    }
    
    @Override
    public RefundResponse refundPayment(RefundRequest request, PaymentGatewayType gatewayType) {
        try {
            IPaymentGateway gateway = getGateway(gatewayType);
            
            log.info("申请退款: gateway={}, orderNo={}, refundOrderNo={}, amount={}", 
                gateway.getGatewayName(), request.getOrderNo(), request.getRefundOrderNo(), request.getRefundAmount());
            
            RefundResponse response = gateway.refundPayment(request);
            
            log.info("退款申请结果: gateway={}, orderNo={}, refundOrderNo={}, success={}, code={}", 
                gateway.getGatewayName(), request.getOrderNo(), request.getRefundOrderNo(), 
                response.isSuccess(), response.getCode());
            
            return response;
        } catch (Exception e) {
            log.error("申请退款失败: orderNo={}, refundOrderNo={}", 
                request.getOrderNo(), request.getRefundOrderNo(), e);
            return RefundResponse.error("GATEWAY_ERROR", "申请退款失败: " + e.getMessage());
        }
    }
    
    @Override
    public CancelResponse cancelPayment(CancelRequest request) {
        return cancelPayment(request, null);
    }
    
    @Override
    public CancelResponse cancelPayment(CancelRequest request, PaymentGatewayType gatewayType) {
        try {
            IPaymentGateway gateway = getGateway(gatewayType);
            
            log.info("撤销支付: gateway={}, orderNo={}", 
                gateway.getGatewayName(), request.getOrderNo());
            
            CancelResponse response = gateway.cancelPayment(request);
            
            log.info("支付撤销结果: gateway={}, orderNo={}, success={}, code={}", 
                gateway.getGatewayName(), request.getOrderNo(), response.isSuccess(), response.getCode());
            
            return response;
        } catch (Exception e) {
            log.error("撤销支付失败: orderNo={}", request.getOrderNo(), e);
            return CancelResponse.error("GATEWAY_ERROR", "撤销支付失败: " + e.getMessage());
        }
    }
    
    @Override
    public boolean verifyNotifySign(String notifyData, PaymentGatewayType gatewayType) {
        try {
            IPaymentGateway gateway = getGateway(gatewayType);
            
            boolean result = gateway.verifyNotifySign(notifyData);
            
            log.info("验证回调签名: gateway={}, result={}", gateway.getGatewayName(), result);
            
            return result;
        } catch (Exception e) {
            log.error("验证回调签名失败: gatewayType={}", gatewayType, e);
            return false;
        }
    }
    
    @Override
    public boolean isOrderCompleted(String orderNo) {
        return isOrderCompleted(orderNo, null);
    }
    
    @Override
    public boolean isOrderCompleted(String orderNo, PaymentGatewayType gatewayType) {
        try {
            IPaymentGateway gateway = getGateway(gatewayType);
            
            boolean result = gateway.isOrderCompleted(orderNo);
            
            log.info("检查订单完成状态: gateway={}, orderNo={}, completed={}", 
                gateway.getGatewayName(), orderNo, result);
            
            return result;
        } catch (Exception e) {
            log.error("检查订单完成状态失败: orderNo={}, gatewayType={}", orderNo, gatewayType, e);
            return false;
        }
    }
    
    private IPaymentGateway getGateway(PaymentGatewayType gatewayType) {
        if (gatewayType != null) {
            return gatewayFactory.getGateway(gatewayType, getPayConfig());
        } else {
            return gatewayFactory.getDefaultGateway();
        }
    }
    
    private PayConfig getPayConfig() {
        try {
            return feePayRecordService.getPayConfigFromSysSetting();
        } catch (Exception e) {
            log.error("获取支付配置失败", e);
            return null;
        }
    }
    
    private String maskAuthCode(String authCode) {
        if (authCode == null || authCode.length() < 6) {
            return authCode;
        }
        return authCode.substring(0, 6) + "****" + authCode.substring(authCode.length() - 4);
    }
}