package org.jeecg.modules.fee.gateway.factory;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.fee.entity.PayConfig;
import org.jeecg.modules.fee.gateway.IPaymentGateway;
import org.jeecg.modules.fee.gateway.config.PaymentGatewayConfig;
import org.jeecg.modules.fee.gateway.enums.PaymentGatewayType;
import org.jeecg.modules.fee.gateway.exception.PaymentGatewayException;
import org.jeecg.modules.fee.gateway.impl.JeePayGateway;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Component
public class PaymentGatewayFactory {
    
    @Resource
    private ApplicationContext applicationContext;
    
    @Resource
    private PaymentGatewayConfig gatewayConfig;
    
    private final Map<String, IPaymentGateway> gatewayCache = new ConcurrentHashMap<>();
    
    public IPaymentGateway getDefaultGateway() {
        return getGateway(gatewayConfig.getDefaultProviderType());
    }
    
    public IPaymentGateway getGateway(String gatewayCode) {
        PaymentGatewayType type = PaymentGatewayType.fromCode(gatewayCode);
        return getGateway(type);
    }
    
    public IPaymentGateway getGateway(PaymentGatewayType type) {
        return getGateway(type, null);
    }
    
    public IPaymentGateway getGateway(PaymentGatewayType type, PayConfig payConfig) {
        String cacheKey = type.getCode() + (payConfig != null ? "_" + payConfig.hashCode() : "");
        
        return gatewayCache.computeIfAbsent(cacheKey, key -> {
            try {
                IPaymentGateway gateway = applicationContext.getBean(type.getBeanName(), IPaymentGateway.class);
                
                if (gateway instanceof JeePayGateway && payConfig != null) {
                    ((JeePayGateway) gateway).init(payConfig);
                }
                
                log.info("成功创建支付网关实例: {}", type.getDescription());
                return gateway;
            } catch (Exception e) {
                log.error("创建支付网关失败: {}", type.getDescription(), e);
                
                if (gatewayConfig.isFallbackEnabled() && !type.equals(gatewayConfig.getFallbackProviderType())) {
                    log.info("尝试使用备用支付网关: {}", gatewayConfig.getFallbackProviderType().getDescription());
                    return getGateway(gatewayConfig.getFallbackProviderType(), payConfig);
                }
                
                throw new PaymentGatewayException("GATEWAY_CREATE_ERROR", 
                    "创建支付网关失败: " + type.getDescription(), e);
            }
        });
    }
    
    public void clearCache() {
        gatewayCache.clear();
        log.info("支付网关缓存已清空");
    }
    
    public void clearCache(PaymentGatewayType type) {
        gatewayCache.entrySet().removeIf(entry -> entry.getKey().startsWith(type.getCode()));
        log.info("已清空支付网关缓存: {}", type.getDescription());
    }
}