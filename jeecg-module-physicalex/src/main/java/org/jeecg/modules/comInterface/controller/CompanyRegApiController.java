package org.jeecg.modules.comInterface.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.comInterface.dto.CompanyRegCreateDTO;
import org.jeecg.modules.comInterface.dto.CustomerRegBatchCreateDTO;
import org.jeecg.modules.comInterface.service.ICompanyRegApiService;
import org.jeecg.modules.comInterface.vo.ApiResponse;
import org.jeecg.modules.comInterface.vo.BatchResultVO;
import org.jeecg.modules.reg.entity.CompanyReg;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Positive;

/**
 * @Description: 企业预约API控制器
 * @Author: system
 * @Date: 2024-07-30
 * @Version: V1.0
 */
@Api(tags = "企业预约API")
@RestController
@RequestMapping("/api/v1/company")
@Slf4j
@Validated
public class CompanyRegApiController {

    @Autowired
    private ICompanyRegApiService companyRegApiService;

    @ApiOperation(value = "创建企业预约", notes = "创建企业预约，包含分组信息")
    @PostMapping("/registration")
    public ApiResponse<CompanyReg> createCompanyReg(@Valid @RequestBody CompanyRegCreateDTO createDTO) {
        try {
            log.info("Creating company registration: {}", createDTO.getRegName());
            CompanyReg companyReg = companyRegApiService.createCompanyReg(createDTO);
            return ApiResponse.success("Company registration created successfully", companyReg);
        } catch (Exception e) {
            log.error("Failed to create company registration", e);
            return ApiResponse.error("Failed to create company registration: " + e.getMessage());
        }
    }

    @ApiOperation(value = "批量创建客户登记", notes = "批量创建客户登记信息")
    @PostMapping("/customers/batch")
    public ApiResponse<BatchResultVO<CustomerReg>> batchCreateCustomerReg(
            @Valid @RequestBody CustomerRegBatchCreateDTO batchCreateDTO) {
        try {
            log.info("Batch creating customer registrations, count: {}", batchCreateDTO.getCustomerList().size());
            BatchResultVO<CustomerReg> result = companyRegApiService.batchCreateCustomerReg(batchCreateDTO);
            
            if (result.getFailureCount() > 0) {
                return ApiResponse.success("Batch creation completed with some failures", result);
            } else {
                return ApiResponse.success("All customer registrations created successfully", result);
            }
        } catch (Exception e) {
            log.error("Failed to batch create customer registrations", e);
            return ApiResponse.error("Failed to batch create customer registrations: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取企业预约信息", notes = "根据ID获取企业预约详细信息")
    @GetMapping("/registration/{companyRegId}")
    public ApiResponse<CompanyReg> getCompanyReg(
            @ApiParam(value = "企业预约ID", required = true)
            @PathVariable @NotBlank String companyRegId) {
        try {
            log.info("Getting company registration: {}", companyRegId);
            CompanyReg companyReg = companyRegApiService.getCompanyRegById(companyRegId);
            
            if (companyReg == null) {
                return ApiResponse.error(404, "Company registration not found");
            }
            
            return ApiResponse.success("Company registration retrieved successfully", companyReg);
        } catch (Exception e) {
            log.error("Failed to get company registration: {}", companyRegId, e);
            return ApiResponse.error("Failed to get company registration: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取客户登记列表", notes = "分页获取指定企业预约下的客户登记列表")
    @GetMapping("/registration/{companyRegId}/customers")
    public ApiResponse<BatchResultVO<CustomerReg>> getCustomerRegList(
            @ApiParam(value = "企业预约ID", required = true)
            @PathVariable @NotBlank String companyRegId,
            @ApiParam(value = "页码", example = "1")
            @RequestParam(defaultValue = "1") @Positive Integer pageNo,
            @ApiParam(value = "页大小", example = "20")
            @RequestParam(defaultValue = "20") @Positive Integer pageSize) {
        try {
            log.info("Getting customer registration list for company: {}, page: {}, size: {}", 
                    companyRegId, pageNo, pageSize);
            
            // 限制页大小
            if (pageSize > 100) {
                pageSize = 100;
            }
            
            BatchResultVO<CustomerReg> result = companyRegApiService.getCustomerRegList(companyRegId, pageNo, pageSize);
            return ApiResponse.success("Customer registration list retrieved successfully", result);
        } catch (Exception e) {
            log.error("Failed to get customer registration list for company: {}", companyRegId, e);
            return ApiResponse.error("Failed to get customer registration list: " + e.getMessage());
        }
    }

    @ApiOperation(value = "健康检查", notes = "API健康检查接口")
    @GetMapping("/health")
    public ApiResponse<String> health() {
        return ApiResponse.success("API is healthy", "OK");
    }
}
