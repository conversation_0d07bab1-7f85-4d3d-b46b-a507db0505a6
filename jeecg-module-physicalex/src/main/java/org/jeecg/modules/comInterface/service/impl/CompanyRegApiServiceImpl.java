package org.jeecg.modules.comInterface.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.excommons.ExConstants;
import org.jeecg.excommons.utils.IdCardUtils;
import org.jeecg.excommons.utils.SequenceGenerator;
import org.jeecg.modules.basicinfo.entity.Company;
import org.jeecg.modules.basicinfo.service.ICompanyService;
import org.jeecg.modules.comInterface.dto.CompanyRegCreateDTO;
import org.jeecg.modules.comInterface.dto.CompanyTeamCreateDTO;
import org.jeecg.modules.comInterface.dto.CustomerRegBatchCreateDTO;
import org.jeecg.modules.comInterface.dto.CustomerRegCreateDTO;
import org.jeecg.modules.comInterface.service.ICompanyRegApiService;
import org.jeecg.modules.comInterface.vo.BatchResultVO;
import org.jeecg.modules.reg.entity.CompanyReg;
import org.jeecg.modules.reg.entity.CompanyTeam;
import org.jeecg.modules.reg.entity.Customer;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.reg.mapper.CompanyRegMapper;
import org.jeecg.modules.reg.mapper.CompanyTeamMapper;
import org.jeecg.modules.reg.mapper.CustomerRegMapper;
import org.jeecg.modules.reg.service.ICompanyRegService;
import org.jeecg.modules.reg.service.ICompanyTeamService;
import org.jeecg.modules.reg.service.ICustomerRegService;
import org.jeecg.modules.reg.service.ICustomerService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import cn.hutool.core.util.PhoneUtil;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @Description: 企业预约API服务实现
 * @Author: system
 * @Date: 2024-07-30
 * @Version: V1.0
 */
@Slf4j
@Service
public class CompanyRegApiServiceImpl implements ICompanyRegApiService {

    @Autowired
    private ICompanyRegService companyRegService;

    @Autowired
    private ICompanyTeamService companyTeamService;

    @Autowired
    private ICustomerRegService customerRegService;

    @Autowired
    private ICustomerService customerService;

    @Autowired
    private ICompanyService companyService;

    @Autowired
    private CompanyRegMapper companyRegMapper;

    @Autowired
    private CompanyTeamMapper companyTeamMapper;

    @Autowired
    private CustomerRegMapper customerRegMapper;

    @Autowired
    private SequenceGenerator sequenceGenerator;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CompanyReg createCompanyReg(CompanyRegCreateDTO createDTO) {
        log.info("Creating company registration: {}", createDTO.getRegName());

        // 创建企业预约
        CompanyReg companyReg = new CompanyReg();
        BeanUtils.copyProperties(createDTO, companyReg);
        
        // 设置默认值
        companyReg.setCheckoutStatus(0);
        companyReg.setLockStatus(0);
        companyReg.setCreateTime(new Date());
        companyReg.setDelFlag(0);
        
        // 生成团报编号
        companyReg.setCompanyReportNo(sequenceGenerator.getFormatedSerialNumBaseToday(ExConstants.SEQ_COMPANY_REPORT_NO));
        
        // 保存企业预约
        companyRegService.save(companyReg);

        // 创建分组
        if (CollectionUtils.isNotEmpty(createDTO.getTeams())) {
            List<CompanyTeam> teams = new ArrayList<>();
            for (CompanyTeamCreateDTO teamDTO : createDTO.getTeams()) {
                CompanyTeam team = new CompanyTeam();
                BeanUtils.copyProperties(teamDTO, team);
                team.setCompanyRegId(companyReg.getId());
                team.setCreateTime(new Date());
                team.setDelFlag(0);
                teams.add(team);
            }
            companyTeamService.saveBatch(teams);
        }

        log.info("Company registration created successfully: {}", companyReg.getId());
        return companyReg;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BatchResultVO<CustomerReg> batchCreateCustomerReg(CustomerRegBatchCreateDTO batchCreateDTO) {
        log.info("Batch creating customer registrations, count: {}", batchCreateDTO.getCustomerList().size());

        String companyRegId = batchCreateDTO.getCompanyRegId();
        
        // 验证企业预约是否存在
        CompanyReg companyReg = companyRegMapper.selectById(companyRegId);
        if (companyReg == null) {
            throw new RuntimeException("Company registration not found: " + companyRegId);
        }

        // 获取分组列表
        List<CompanyTeam> companyTeams = companyTeamMapper.selectByMainId(companyReg.getId());
        if (CollectionUtils.isEmpty(companyTeams)) {
            throw new RuntimeException("No teams found for company registration: " + companyRegId);
        }

        BatchResultVO<CustomerReg> result = new BatchResultVO<>();
        List<CustomerReg> successList = new ArrayList<>();
        List<BatchResultVO.FailureItem<CustomerReg>> failureList = new ArrayList<>();

        for (CustomerRegCreateDTO customerDTO : batchCreateDTO.getCustomerList()) {
            try {
                CustomerReg customerReg = processCustomerReg(customerDTO, companyReg, companyTeams);
                if (customerReg != null) {
                    successList.add(customerReg);
                }
            } catch (Exception e) {
                log.error("Failed to create customer registration for: {}", customerDTO.getName(), e);
                CustomerReg failedCustomer = new CustomerReg();
                BeanUtils.copyProperties(customerDTO, failedCustomer);
                failureList.add(new BatchResultVO.FailureItem<>(failedCustomer, e.getMessage()));
            }
        }

        result.setTotal(batchCreateDTO.getCustomerList().size());
        result.setSuccessCount(successList.size());
        result.setFailureCount(failureList.size());
        result.setSuccessList(successList);
        result.setFailureList(failureList);

        log.info("Batch creation completed. Success: {}, Failure: {}", successList.size(), failureList.size());
        return result;
    }

    @Override
    public CompanyReg getCompanyRegById(String companyRegId) {
        return companyRegMapper.selectById(companyRegId);
    }

    @Override
    public BatchResultVO<CustomerReg> getCustomerRegList(String companyRegId, Integer pageNo, Integer pageSize) {
        Page<CustomerReg> page = new Page<>(pageNo, pageSize);
        LambdaQueryWrapper<CustomerReg> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CustomerReg::getCompanyRegId, companyRegId);
        queryWrapper.eq(CustomerReg::getDelFlag, 0);
        queryWrapper.orderByDesc(CustomerReg::getCreateTime);

        Page<CustomerReg> resultPage = customerRegMapper.selectPage(page, queryWrapper);

        BatchResultVO<CustomerReg> result = new BatchResultVO<>();
        result.setTotal((int) resultPage.getTotal());
        result.setSuccessCount((int) resultPage.getTotal());
        result.setFailureCount(0);
        result.setSuccessList(resultPage.getRecords());
        result.setFailureList(new ArrayList<>());

        return result;
    }

    /**
     * 处理单个客户登记
     */
    private CustomerReg processCustomerReg(CustomerRegCreateDTO customerDTO, CompanyReg companyReg, List<CompanyTeam> companyTeams) {
        // 创建CustomerReg对象
        CustomerReg customerReg = new CustomerReg();
        BeanUtils.copyProperties(customerDTO, customerReg);

        // 设置企业信息
        customerReg.setCompanyRegId(companyReg.getId());
        customerReg.setCompanyRegName(companyReg.getRegName());
        customerReg.setCompanyId(companyReg.getCompanyId());
        customerReg.setCompanyName(companyReg.getCompanyName());

        // 处理姓名（去除空格）
        String name = customerReg.getName();
        customerReg.setName(name.replaceAll("\\s", ""));

        // 验证数据
        validateCustomerReg(customerReg);

        // 检查是否已存在
        boolean updateFlag = false;
        List<CustomerReg> existingCustomers = customerRegService.list(
            new LambdaQueryWrapper<CustomerReg>()
                .eq(CustomerReg::getIdCard, customerReg.getIdCard())
                .eq(CustomerReg::getCompanyRegId, companyReg.getId())
                .orderByDesc(CustomerReg::getExamNo)
        );

        if (CollectionUtils.isNotEmpty(existingCustomers)) {
            CustomerReg existing = existingCustomers.get(0);
            if (StringUtils.equals(existing.getStatus(), "未登记")) {
                updateFlag = true;
                customerReg.setId(existing.getId());
                customerReg.setExamNo(existing.getExamNo());
            } else {
                throw new RuntimeException("Customer with ID card already registered: " + customerReg.getIdCard());
            }
        } else {
            // 生成体检号
            customerReg.setExamNo(sequenceGenerator.getFormatedSerialNumBaseToday(ExConstants.SEQ_EXAMNO));
        }

        // 处理身份证信息
        processIdCard(customerReg);

        // 匹配分组
        CompanyTeam matchedTeam = matchTeam(customerReg, companyTeams);
        if (matchedTeam == null) {
            throw new RuntimeException("No suitable team found for customer: " + customerReg.getName());
        }

        // 设置分组信息
        customerReg.setTeamId(matchedTeam.getId());
        customerReg.setTeamNum(matchedTeam.getTeamNum());
        customerReg.setTeamName(matchedTeam.getName());
        customerReg.setRiskFactor(StringUtils.isNotBlank(customerReg.getRiskFactor()) ? 
            customerReg.getRiskFactor() : matchedTeam.getRisks());
        customerReg.setJobStatus(StringUtils.isNotBlank(customerReg.getJobStatus()) ? 
            customerReg.getJobStatus() : matchedTeam.getPost());

        // 处理部门信息
        processDepartment(customerReg);

        // 设置其他默认值
        setDefaultValues(customerReg);

        // 处理工龄信息
        processWorkYears(customerReg);

        // 创建或更新Customer
        Customer customer = saveCustomerWithRateLimit(customerReg);
        customerReg.setCustomerId(customer.getId());
        customerReg.setArchivesNum(customer.getArchivesNum());

        // 保存CustomerReg
        if (updateFlag) {
            customerRegService.updateById(customerReg);
        } else {
            customerRegService.save(customerReg);
        }

        log.info("Customer registration processed: {}", customerReg.getName());
        return customerReg;
    }

    /**
     * 验证客户登记数据
     */
    private void validateCustomerReg(CustomerReg customerReg) {
        if (StringUtils.isBlank(customerReg.getName())) {
            throw new RuntimeException("Name cannot be empty");
        }
        if (StringUtils.isBlank(customerReg.getGender())) {
            throw new RuntimeException("Gender cannot be empty");
        }
        if (customerReg.getBirthday() == null) {
            throw new RuntimeException("Birthday cannot be empty");
        }
        if (!PhoneUtil.isPhone(customerReg.getPhone())) {
            throw new RuntimeException("Invalid phone number: " + customerReg.getPhone());
        }
    }

    /**
     * 处理身份证信息
     */
    private void processIdCard(CustomerReg customerReg) {
        String idCard = customerReg.getIdCard();
        String idCardType = customerReg.getCardType();
        idCardType = StringUtils.isBlank(idCardType) ? "居民身份证" : idCardType;
        customerReg.setCardType(idCardType);

        if (StringUtils.equals(idCardType, "居民身份证")) {
            if (StringUtils.isNotBlank(idCard)) {
                if (!IdCardUtils.isValidCard(idCard)) {
                    throw new RuntimeException("Invalid ID card number: " + idCard);
                } else {
                    customerReg.setBirthday(IdCardUtils.getBirthDate(idCard));
                    customerReg.setAge(IdCardUtils.getAge(idCard));
                    customerReg.setGender(IdCardUtils.getGender(idCard));
                }
            }
        }
    }

    /**
     * 匹配分组
     */
    private CompanyTeam matchTeam(CustomerReg customerReg, List<CompanyTeam> companyTeams) {
        if (StringUtils.isNotBlank(customerReg.getTeamName())) {
            // 根据分组名称匹配
            return companyTeams.stream()
                .filter(team -> StringUtils.equals(team.getName(), customerReg.getTeamName()))
                .findFirst()
                .orElse(null);
        } else {
            // 根据条件自动匹配
            return companyTeams.stream()
                .filter(team -> team.contains(customerReg.getAge(), customerReg.getGender(), customerReg.getMarriageStatus()))
                .findFirst()
                .orElse(null);
        }
    }

    /**
     * 处理部门信息
     */
    private void processDepartment(CustomerReg customerReg) {
        if (StringUtils.isNotBlank(customerReg.getCompanyDeptId())) {
            Company dept = companyService.getById(customerReg.getCompanyDeptId());
            if (Objects.nonNull(dept)) {
                customerReg.setCompanyDeptName(dept.getName());
            }
        }
        if (StringUtils.isNotBlank(customerReg.getCompanyDeptName())) {
            Company dept = companyService.getOne(
                new LambdaQueryWrapper<Company>()
                    .eq(Company::getName, customerReg.getCompanyDeptName())
                    .eq(Company::getPid, customerReg.getCompanyId())
                    .last("limit 1")
            );
            if (Objects.nonNull(dept)) {
                customerReg.setCompanyDeptId(dept.getId());
            }
        }
    }

    /**
     * 设置默认值
     */
    private void setDefaultValues(CustomerReg customerReg) {
        customerReg.setAgeUnit("岁");
        customerReg.setCustomerCategory("企业客户");
        customerReg.setCreateTime(new Date());
        customerReg.setStatus(ExConstants.REG_STATUS_WAIT);
        customerReg.setPaymentState(ExConstants.PAY_STATUS_WAIT);
        customerReg.setDelFlag("0");
        customerReg.setRetrieveStatus("0");

        Date appointmentDate = customerReg.getAppointmentDate();
        if (appointmentDate == null) {
            appointmentDate = new Date();
            customerReg.setAppointmentDate(appointmentDate);
        }
        if (customerReg.getAppointmentSort() == null) {
            LocalDate localDate = appointmentDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            customerReg.setAppointmentSort(sequenceGenerator.getSerialNoBaseDate(ExConstants.SEQ_APPOINTMENT_NO, localDate));
        }
    }

    /**
     * 处理工龄信息
     */
    private void processWorkYears(CustomerReg customerReg) {
        if (StringUtils.isNotBlank(customerReg.getWorkYears()) && StringUtils.isBlank(customerReg.getWorkMonths())) {
            String[] workYearAndMonth = StringUtils.split(customerReg.getWorkYears(), ".");
            if (workYearAndMonth.length == 2) {
                customerReg.setWorkYears(workYearAndMonth[0]);
                customerReg.setWorkMonths(workYearAndMonth[1]);
            } else {
                customerReg.setWorkYears(workYearAndMonth[0]);
                customerReg.setWorkMonths("0");
            }
        }

        if (StringUtils.isNotBlank(customerReg.getRiskYears()) && StringUtils.isBlank(customerReg.getRiskMonths())) {
            String[] riskYearAndMonth = StringUtils.split(customerReg.getRiskYears(), ".");
            if (riskYearAndMonth.length == 2) {
                customerReg.setRiskYears(riskYearAndMonth[0]);
                customerReg.setRiskMonths(riskYearAndMonth[1]);
            } else {
                customerReg.setRiskYears(riskYearAndMonth[0]);
                customerReg.setRiskMonths("0");
            }
        }
    }

    /**
     * 创建Customer（限流版本）
     */
    private Customer saveCustomerWithRateLimit(CustomerReg customerReg) {
        // 这里应该调用现有的customerService方法
        // 为了简化，直接创建一个基本的Customer对象
        Customer customer = new Customer();
        customer.setName(customerReg.getName());
        customer.setGender(customerReg.getGender());
        customer.setIdCard(customerReg.getIdCard());
        customer.setCardType(customerReg.getCardType());
        customer.setBirthday(customerReg.getBirthday());
        customer.setAge(customerReg.getAge());
        customer.setPhone(customerReg.getPhone());
        customer.setCreateTime(new Date());
        customer.setDelFlag(0);
        
        // 生成档案号
        customer.setArchivesNum(sequenceGenerator.getFormatedSerialNumBaseToday(ExConstants.SEQ_ARCHIVES_NUM));
        
        customerService.save(customer);
        return customer;
    }
}
