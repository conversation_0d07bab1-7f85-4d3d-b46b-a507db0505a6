package org.jeecg.modules.comInterface.interceptor;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.modules.comInterface.entity.ApiClient;
import org.jeecg.modules.comInterface.service.IApiClientService;
import org.jeecg.modules.comInterface.util.ApiSignUtil;
import org.jeecg.modules.comInterface.vo.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Date;

/**
 * @Description: API鉴权拦截器
 * @Author: system
 * @Date: 2024-07-30
 * @Version: V1.0
 */
@Slf4j
@Component
public class ApiAuthInterceptor implements HandlerInterceptor {

    @Autowired
    private IApiClientService apiClientService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 获取请求头中的鉴权信息
        String apiKey = request.getHeader("X-API-Key");
        String timestamp = request.getHeader("X-Timestamp");
        String nonce = request.getHeader("X-Nonce");
        String signature = request.getHeader("X-Signature");

        // 检查必要参数
        if (StringUtils.isAnyBlank(apiKey, timestamp, nonce, signature)) {
            log.warn("API authentication failed: missing required headers");
            writeErrorResponse(response, ApiResponse.badRequest("Missing required authentication headers"));
            return false;
        }

        // 验证时间戳（5分钟内有效）
        try {
            long requestTime = Long.parseLong(timestamp);
            long currentTime = System.currentTimeMillis();
            if (Math.abs(currentTime - requestTime) > 5 * 60 * 1000) {
                log.warn("API authentication failed: timestamp expired, apiKey: {}", apiKey);
                writeErrorResponse(response, ApiResponse.unauthorized("Request timestamp expired"));
                return false;
            }
        } catch (NumberFormatException e) {
            log.warn("API authentication failed: invalid timestamp format, apiKey: {}", apiKey);
            writeErrorResponse(response, ApiResponse.badRequest("Invalid timestamp format"));
            return false;
        }

        // 查询API客户端配置
        ApiClient apiClient = apiClientService.getByApiKey(apiKey);
        if (apiClient == null) {
            log.warn("API authentication failed: invalid apiKey: {}", apiKey);
            writeErrorResponse(response, ApiResponse.unauthorized("Invalid API key"));
            return false;
        }

        // 检查客户端状态
        if (apiClient.getStatus() == null || apiClient.getStatus() != 1) {
            log.warn("API authentication failed: client disabled, apiKey: {}", apiKey);
            writeErrorResponse(response, ApiResponse.forbidden("API client is disabled"));
            return false;
        }

        // 检查过期时间
        if (apiClient.getExpireTime() != null && apiClient.getExpireTime().before(new Date())) {
            log.warn("API authentication failed: client expired, apiKey: {}", apiKey);
            writeErrorResponse(response, ApiResponse.forbidden("API client has expired"));
            return false;
        }

        // 检查IP白名单
        if (StringUtils.isNotBlank(apiClient.getIpWhitelist())) {
            String clientIp = getClientIpAddress(request);
            String[] allowedIps = apiClient.getIpWhitelist().split(",");
            boolean ipAllowed = false;
            for (String allowedIp : allowedIps) {
                if (allowedIp.trim().equals(clientIp)) {
                    ipAllowed = true;
                    break;
                }
            }
            if (!ipAllowed) {
                log.warn("API authentication failed: IP not in whitelist, apiKey: {}, clientIp: {}", apiKey, clientIp);
                writeErrorResponse(response, ApiResponse.forbidden("IP address not allowed"));
                return false;
            }
        }

        // 验证签名
        String expectedSignature = ApiSignUtil.generateSignature(apiKey, apiClient.getApiSecret(), timestamp, nonce);
        if (!signature.equals(expectedSignature)) {
            log.warn("API authentication failed: invalid signature, apiKey: {}", apiKey);
            writeErrorResponse(response, ApiResponse.unauthorized("Invalid signature"));
            return false;
        }

        // 将API客户端信息存储到请求属性中，供后续使用
        request.setAttribute("apiClient", apiClient);
        
        log.info("API authentication success, apiKey: {}, clientName: {}", apiKey, apiClient.getClientName());
        return true;
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (StringUtils.isNotBlank(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (StringUtils.isNotBlank(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }

    /**
     * 写入错误响应
     */
    private void writeErrorResponse(HttpServletResponse response, ApiResponse<?> apiResponse) throws IOException {
        response.setContentType("application/json;charset=UTF-8");
        response.setStatus(apiResponse.getCode());
        
        try (PrintWriter writer = response.getWriter()) {
            writer.write(JSON.toJSONString(apiResponse));
            writer.flush();
        }
    }
}
