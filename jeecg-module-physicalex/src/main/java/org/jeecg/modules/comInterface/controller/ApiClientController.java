package org.jeecg.modules.comInterface.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.comInterface.entity.ApiClient;
import org.jeecg.modules.comInterface.service.IApiClientService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.Date;

/**
 * @Description: API客户端配置管理
 * @Author: system
 * @Date: 2024-07-30
 * @Version: V1.0
 */
@Api(tags = "API客户端配置管理")
@RestController
@RequestMapping("/comInterface/apiClient")
@Slf4j
public class ApiClientController extends JeecgController<ApiClient, IApiClientService> {

    @Autowired
    private IApiClientService apiClientService;

    /**
     * 分页列表查询
     */
    @AutoLog(value = "API客户端配置-分页列表查询")
    @ApiOperation(value = "API客户端配置-分页列表查询", notes = "API客户端配置-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<ApiClient>> queryPageList(ApiClient apiClient,
                                                  @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                  @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                  HttpServletRequest req) {
        QueryWrapper<ApiClient> queryWrapper = QueryGenerator.initQueryWrapper(apiClient, req.getParameterMap());
        Page<ApiClient> page = new Page<>(pageNo, pageSize);
        IPage<ApiClient> pageList = apiClientService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     */
    @AutoLog(value = "API客户端配置-添加")
    @ApiOperation(value = "API客户端配置-添加", notes = "API客户端配置-添加")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody ApiClient apiClient) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        
        // 生成API密钥和秘钥
        ApiClient generated = apiClientService.generateApiKeys();
        apiClient.setApiKey(generated.getApiKey());
        apiClient.setApiSecret(generated.getApiSecret());
        apiClient.setStatus(generated.getStatus());
        apiClient.setDelFlag(generated.getDelFlag());
        
        apiClient.setCreateBy(sysUser.getUsername());
        apiClient.setCreateTime(new Date());
        
        apiClientService.save(apiClient);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     */
    @AutoLog(value = "API客户端配置-编辑")
    @ApiOperation(value = "API客户端配置-编辑", notes = "API客户端配置-编辑")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody ApiClient apiClient) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        apiClient.setUpdateBy(sysUser.getUsername());
        apiClient.setUpdateTime(new Date());
        apiClientService.updateById(apiClient);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     */
    @AutoLog(value = "API客户端配置-通过id删除")
    @ApiOperation(value = "API客户端配置-通过id删除", notes = "API客户端配置-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        apiClientService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     */
    @AutoLog(value = "API客户端配置-批量删除")
    @ApiOperation(value = "API客户端配置-批量删除", notes = "API客户端配置-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.apiClientService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     */
    @AutoLog(value = "API客户端配置-通过id查询")
    @ApiOperation(value = "API客户端配置-通过id查询", notes = "API客户端配置-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<ApiClient> queryById(@RequestParam(name = "id", required = true) String id) {
        ApiClient apiClient = apiClientService.getById(id);
        if (apiClient == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(apiClient);
    }

    /**
     * 重新生成API密钥
     */
    @AutoLog(value = "API客户端配置-重新生成API密钥")
    @ApiOperation(value = "API客户端配置-重新生成API密钥", notes = "API客户端配置-重新生成API密钥")
    @PostMapping(value = "/regenerateKeys")
    public Result<String> regenerateKeys(@RequestParam(name = "id", required = true) String id) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        
        ApiClient apiClient = apiClientService.getById(id);
        if (apiClient == null) {
            return Result.error("未找到对应数据");
        }
        
        // 生成新的API密钥和秘钥
        ApiClient generated = apiClientService.generateApiKeys();
        apiClient.setApiKey(generated.getApiKey());
        apiClient.setApiSecret(generated.getApiSecret());
        apiClient.setUpdateBy(sysUser.getUsername());
        apiClient.setUpdateTime(new Date());
        
        apiClientService.updateById(apiClient);
        return Result.OK("API密钥重新生成成功！");
    }

    /**
     * 启用/禁用API客户端
     */
    @AutoLog(value = "API客户端配置-启用/禁用")
    @ApiOperation(value = "API客户端配置-启用/禁用", notes = "API客户端配置-启用/禁用")
    @PostMapping(value = "/toggleStatus")
    public Result<String> toggleStatus(@RequestParam(name = "id", required = true) String id,
                                       @RequestParam(name = "status", required = true) Integer status) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        
        ApiClient apiClient = apiClientService.getById(id);
        if (apiClient == null) {
            return Result.error("未找到对应数据");
        }
        
        apiClient.setStatus(status);
        apiClient.setUpdateBy(sysUser.getUsername());
        apiClient.setUpdateTime(new Date());
        
        apiClientService.updateById(apiClient);
        return Result.OK(status == 1 ? "启用成功！" : "禁用成功！");
    }

    /**
     * 导出excel
     */
    @RequestMapping(value = "/exportXls")
    public void exportXls(ApiClient apiClient, HttpServletRequest request, HttpServletResponse response) {
        // 这里可以实现导出功能
    }

    /**
     * 通过excel导入数据
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        // 这里可以实现导入功能
        return Result.OK("导入成功");
    }
}
