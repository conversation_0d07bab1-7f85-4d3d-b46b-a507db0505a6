package org.jeecg.modules.comInterface.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.comInterface.entity.ApiClient;

/**
 * @Description: API客户端配置Service接口
 * @Author: system
 * @Date: 2024-07-30
 * @Version: V1.0
 */
public interface IApiClientService extends IService<ApiClient> {

    /**
     * 根据API密钥获取客户端配置
     * 
     * @param apiKey API密钥
     * @return API客户端配置
     */
    ApiClient getByApiKey(String apiKey);

    /**
     * 生成API密钥和秘钥
     * 
     * @return API客户端配置
     */
    ApiClient generateApiKeys();
}
