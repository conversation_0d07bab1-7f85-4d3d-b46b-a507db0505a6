package org.jeecg.modules.electronicbill.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

/**
 * 电子票据配置类
 * 
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "electronic.bill")
public class ElectronicBillConfig {

    /**
     * 电子票据平台API地址
     */
    private String apiUrl = "http://localhost:8080/api/electronicBill";

    /**
     * 机构编码
     */
    private String orgCode = "DEFAULT_ORG";

    /**
     * 开票点编码
     */
    private String defaultPlaceCode = "DEFAULT_PLACE";

    /**
     * 私钥（用于签名）
     */
    private String privateKey = "DEFAULT_PRIVATE_KEY";

    /**
     * 公钥（用于验签）
     */
    private String publicKey = "DEFAULT_PUBLIC_KEY";

    /**
     * 接口超时时间（毫秒）
     */
    private Integer timeoutMs = 30000;

    /**
     * 是否启用签名验证
     */
    private Boolean enableSignature = true;

    /**
     * 是否启用电子票据功能
     */
    private Boolean enabled = true;

    /**
     * 重试次数
     */
    private Integer retryTimes = 3;

    /**
     * 业务标识配置
     */
    private BusType busType = new BusType();

    @Data
    public static class BusType {
        private String outpatient = "02"; // 门诊
        private String hospitalized = "01"; // 住院
        private String registration = "03"; // 挂号
        private String physicalExam = "04"; // 体检
    }

    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }
}