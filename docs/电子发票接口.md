**福建博思软件股份有限公司**

 

 

 

 

 

 

***\*医疗电子票据管理平台\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml17576\wps1.png)***\*接口规范说明书\****

 

 

 

 

 

 

 

**文档编号：**

**版本信息：**V**3****.****2****.****0**

**编写日期：**2017-12-25





**文件修订记录**

| ***\*序号\**** | ***\*版本号\**** | ***\*修订日期\**** | ***\*修订概述\**** | ***\*备注\****                                               |
| -------------- | ---------------- | ------------------ | ------------------ | ------------------------------------------------------------ |
| 1              | v1.0.0           | 2021-4-25          | 新增               |                                                              |
| 2              | V1.0.1           | 2021-06-21         | 修改               | 开票接口新增isArrears是否可流通，arrearsReason不可流通原因；门诊开票接口新增chargeDate交费日期（内蒙）。 |
| 3              | V1.0.2           | 2021-06-30         | 修改               | 1、更新开票接口medCareInstitution、balancedNumber、code、name为必传信息。2、开票明细清单his需将医保标识\|费款所属期\|执行科室\|规格 拼接后传入remark备注字段中。 |

 

**
****目  录**

**
**

[1 前言 ](#_Toc31348 )

[1.1 目的 ](#_Toc30412 )

[1.2 预期读者 ](#_Toc3513 )

[1.3 背景 ](#_Toc14596 )

[2 接口规范说明 ](#_Toc27418 )

[2.1 访问协议 ](#_Toc27751 )

[2.2 访问授权 ](#_Toc18309 )

[2.3 参数规范 ](#_Toc26631 )

[2.3.1 服务请求方发送请求 ](#_Toc17440 )

[2.3.2 服务提供者返回请求结果 ](#_Toc15719 )

[2.4 签名规则 ](#_Toc5513 )

[2.4.1 签名算法 ](#_Toc15154 )

[2.4.2 请求随机标识算法 ](#_Toc4665 )

[2.5 请求与返回参数构成 ](#_Toc32394 )

[2.6 通用失败应答 ](#_Toc18713 )

[2.7 通用成功应答 ](#_Toc20443 )

[3 业务流程 ](#_Toc2425 )

[3.1 接口列表 ](#_Toc12129 )

[3.2 接口对接说明 ](#_Toc20495 )

[3.2.1 电子票据开具与冲红 ](#_Toc22209 )

[3.2.2 电子票据换开与数据核对 ](#_Toc26796 )

[3.2.3 电子票据查询与库存信息 ](#_Toc24663 )

[4 接口描述 ](#_Toc21184 )

[4.1 电子票据 ](#_Toc2615 )

[4.1.1 电子票据开具接口 ](#_Toc9964 )

[4.1.2 电子票据冲红接口 ](#_Toc31215 )

[4.1.3 根据电子票信息获取电子票据状态接口 ](#_Toc28207 )

[4.1.4 电子票据流通状态反馈接口 ](#_Toc20357 )

[4.1.5 打印电子票据接口 ](#_Toc8051 )

[4.1.6 获取电子票据清单总页数接口 ](#_Toc2381 )

[4.1.7 打印电子票据清单接口 ](#_Toc24193 )

[4.2 财务核对 ](#_Toc1931 )

[4.2.1 数据核对 ](#_Toc12205 )

[附录1：JSON说明 ](#_Toc13816 )

[附录2：数据格式 ](#_Toc11697 )

[附录3：卡类型列表 ](#_Toc21159 )

[附录4：交费渠道列表 ](#_Toc31633 )

[附录5：业务标识列表 ](#_Toc17630 )

[附录6：通知类型 ](#_Toc14981 )

[附录7：医保报销类型列表 ](#_Toc19417 )

[附录8：编码对照说明 ](#_Toc19506 )

[附录9：返回结果标识列表 ](#_Toc19609 )





# **1** ***\*前言\****

## **1.1** ***\*目的\****

立足当前财政电子票据管理改革的需求，结合目前执收单位业务系统现状，整体建设目标如下：

**一是满足财政电子票据改革的需要。**

严格遵循财政部的财政电子票据管理改革的标准规范和业务要求，完成电子票据的开具、传输、查验、入账、归档，满足财政统一规范财政电子票据管理的需求。

**二是医院业务系统的快速接入。**

现有的医院收费系统只需进行简单改造，通过调用接口的方式即可快速满足财政电子票据管理要求，在不影响业务操作和用户体验的基础上提高系统建设进度的同时又充分保护了现有投资，避免了重复投资和浪费。

## **1.2** ***\*预期读者\****

业务系统对应的开发者

## **1.3** ***\*背景\****

随着财政电子票据的推进，需要将传统的医疗票据转化为医疗电子票据，满足财政电子票据改革的需要。

# **2** ***\*接口规范说明\****

## **2.1** ***\*访问协议\****

统一技术标准，采用基于HTTP协议的进行交互，提交方式使用POST。

| **传输方式**             | 支持HTTP/HTTPS传输                                           |
| ------------------------ | ------------------------------------------------------------ |
| **提交方式**             | POST                                                         |
| **数据****传输****格式** | 提交和返回数据可以为JSON格式（Content-Type: application/json） |
| **字符编码**             | 统一采用UTF-8字符编码                                        |
| **签名算法**             | MD5                                                          |
| **签名要求**             | 请求和接收数据均需要校验签名，详细方法请参考《签名规则-签名算法》 |
| **API服务地址**          | API服务地址规则：http://<ip>:<port>/<service>/api/medical/接口服务标识注：serveice指医疗电子票据管理平台部署的服务名称 |

## **2.2** ***\*访问授权\****

接口调用方在调用API服务之前，必须获取接口服务提供方的访问授权，即appid和签名私钥key（需要平台提供）。

## **2.3** ***\*参数\*******\*规范\****

### **2.3.1** ***\*服务\*******\*请求方\*******\*发送请求\****

请求参数内容：

其中data为请求业务参数，根据不同的接口构造参数；其余的参数为公用参数。

| ***\*参数中文名称\**** | ***\*参数名\**** | ***\*类型定义\**** | ***\*必填\**** | ***\*说明\****                                               |
| ---------------------- | ---------------- | ------------------ | -------------- | ------------------------------------------------------------ |
| 应用帐号               | appid            | String             | 是             | 调用方应用帐号，由平台提供                                   |
| 请求业务参数           | data             | String             | 是             | 各接口请求业务参数的内容不同，实际内容以各接口接口为准；接口请求业务参数值，参数JSON的格式，并且做base64编码,编码字符集UTF-8 |
| 请求随机标识           | noise            | String             | 是             | 每次请求生成一个唯一编号，全局唯一（建议采用UUID）。         |
| 版本号                 | version          | String             | 是             | 默认1.0                                                      |
| 签名                   | sign             | String             | 是             | MD5摘要结果转换成大写                                        |

1、 请求方参数主要包括应用帐号appid 、data、随机标识noise、签名sign。

签名规则详见-2.4。

2、 参数data内容，采用base64编码，字符集UTF-8，参考2.5。

注：key的值必须与appid对应

### **2.3.2** ***\*服务提供者返回请求结果\****

响应参数内容：

其中data为返回结果参数，根据不同的接口构造参数；其余的参数为公用参数

| ***\*参数中文名称\**** | ***\*参数名\**** | ***\*类型定义\**** | ***\*是否必填\**** | ***\*说明\****                                               |
| ---------------------- | ---------------- | ------------------ | ------------------ | ------------------------------------------------------------ |
| 返回结果参数           | data             | String             | 是                 | 各API调用返回的内容不同，实际内容以各接口API为准，参数值为JSON格式做base64编码，编码字符集UTF-8 |
| 返回随机标识           | noise            | String             | 是                 | 每次请求返回一个唯一编号，全局唯一（建议采用UUID）。         |
| 签名                   | sign             | String             | 是                 | MD5摘要结果转换成大写                                        |

1、 接口返回内容主要包括结果data、随机标示noise、签名sign。

2、 服务请求方在接收到响应参数后要进行解密、解析处理；

签名规则详见-2.4。

3、 在验签成功后，将data做BASE64解码，读取解码结果。

#### **2.3.2.1** ***\*返回结果参数data说明\****

返回结果的中的签名规则与请求中签名规则一致。

1、 请求成功时，返回json（返回结果参数message）格式如：

{"result":"S0000","message":"BASE64(响应业务参数)"}。

请求成功返回结果，请求方读取message参数中的内容做BASE64解码，读取解码结果。

2、 请求失败时，返回json（返回结果参数message）格式如：

{"result":"E0001","message":"BASE64(错误信息)"}。

请求失败返回结果，请求方只需要读取data参数中的内容BASE64解码，读取解码结果。

返回节点为”result”,值为S0000，表示成功；

返回节点为”result”,值为其它值（result <> S0000），表示失败。

## **2.4** ***\*签名规则\****

为了防止用户信息被抓包获取后，进行信息篡改，需要对所有参数（不包括签名字段）进行签名加密。

接口调用方和接口接收方对所有的参数节点，使键值对的格式key=value拼接字符串（键值对使用&符号拼接），拼接字符串需要包含秘钥key值（“&key=签名私钥key”字符串），根据最终字符串做MD5加密结果转成大写，生成签名串sign。

### **2.4.1** ***\*签名算法\****

特别注意以下重要规则：

◆ 参数名按固定顺序排序进行拼接；

◆ 参数名区分大小写；

◆ 传送的sign参数不参与签名。 

例如待传送的参数如下：

{

 "appid ": "app1 "

 "data ":"ewogI ",-- 各接口参数内容做BASE64编码

 "noise ":"ibuaiVcKdpRxkhJ ",

 "version ": "1.0",

}

**第一步：**对参数按照key=value的格式，并按照固定顺序排序如下：

stringA="appid=app1&data=ewogI&noise=ibuaiVcKdpRxkhJA"。

**第二步：**拼接API密钥和版本号：

stringSignTemp=stringA+"&key=192006250b4c09247ec02f6a2d&version=1.0" //注：key为接口提供方发放的签名私钥key。

sign=MD5(stringSignTemp).toUpperCase()="E9324CF02F95CB072B6DBCEA33E725C3 " //注：MD5签名方式。

**第三步：**最终得到发送的数据：

{

 "appid":"app1"

 "data": "ewogI",-- 各接口参数内容做BASE64编码

 "noise": "ibuaiVcKdpRxkhJA",

 "version": "1.0",

 "sign": "E9324CF02F95CB072B6DBCEA33E725C3"

}

### **2.4.2** ***\*请求随机标识\*******\*算法\****

API接口协议中包含字段noise，主要保证签名不可预测。我们推荐生成随机数算法如下：调用随机数函数生成或当前时间戳，将得到的值转换为字符串。

## **2.5** ***\*请求与返回\*******\*参数\*******\*构成\****

l 请求URL：[http://[ip\]:[port]/[service]/api/medical/ [接口服务标识](http://[ip]:[port]/[service]/api/hospital/ v1/ [rest-api)]

Ø ip和port：由平台提供，为实际部署环境

Ø service：由平台提供，为实际部署服务名称

Ø 接口服务标识：参考各接口中对应的服务标识

l 请求提交方式：POST

l 请求参数数据格式：以JSON封装如

Ø 请求参数示例：

JSON中data参数明文内容：

{

  "appid": "7e7f4e61189145c1a5c2cce38a4219b3",

  "data": {

​    "billBatchCode": "**********",

​    "billNo": " **********",

​    "random": "DFG456"

  },

  "noise": "ibuaiVcKdpRxkhJAXXXX",

"version": "1.0",

  "sign": "E9324CF02F95CB072B6DBCEA33E725C3"

}

JSON中data参数最终请求内容，data对应的原json格式内容做base64：

{

  "appid": "7e7f4e61189145c1a5c2cce38a4219b3",

  "data": "ewogICAgICAgICJlQmlsbEJhdGNoQ29kZSI6ICIwMTAwMDAwMTAwIiwKICAgICAgICAiZUJpbGxObyI6ICIwMDAwMDAwMDAxIiwKICAgICAgICAiZVJhbmRvbSI6ICJERkc0NTYiCiB9",

"noise": "ibuaiVcKdpRxkhJAXXXXX",

"version": "1.0",

  "sign": "E9324CF02F95CB072B6DBCEA33E725C3"

}

Ø 返回参数示例：

JSON中data参数明文内容：

{

  "data": {

​      "result": "E0001",--除S0000表示成功标识，其它都为错误标识

​      "message": "BASE64(错误信息)"

  },

  "noise": "ibuaiVcKdpRxkhJAXXXX",

  "sign": "E9324CF02F95CB072B6DBCEA33E725C3"

}

JSON中data参数最终返回内容，base64：

{

  "data": "ewogICAgICAgICJlQmlsbEJhdGNoQ29kZSI6ICIwMTAwMDAwMTAwIiwKICAgICAgICAiZUJpbGxObyI6ICIwMDAwMDAwMDAxIiwKICAgICAgICAiZVJhbmRvbSI6ICJERkc0NTYiCiB9",

  "noise": "ibuaiVcKdpRxkhJAXXXX",

  "sign": "E9324CF02F95CB072B6DBCEA33E725C3"

}

## **2.6** ***\*通用失败应答\****

所有接口返回失败信息格式，节点：返回参数data内容，适用于所有API。

| **参数** | **描述**     | **类型** | **长度** | **必填** | **补充**                                       |
| -------- | ------------ | -------- | -------- | -------- | ---------------------------------------------- |
| result   | 返回结果标识 | String   | 10       | 是       | 除S0000代码值之外的代码，都表示失败：如E0001等 |
| message  | 返回结果内容 | String   | 不限     | 是       | BASE64(错误信息)                               |

## **2.7** ***\*通用成功应答\****

通用返回成功信息格式，节点：返回参数data内容，适用于所有API。

| **参数** | **描述**     | **类型** | **长度** | **必填** | **补充**         |
| -------- | ------------ | -------- | -------- | -------- | ---------------- |
| result   | 返回结果标识 | String   | 10       | 是       | S0000            |
| message  | 返回结果内容 | String   | 不限     | 是       | BASE64(成功信息) |

 

# **3** ***\*业务流程\****

## **3.1** ***\*接口列表\****

注：财务入账、数据核对类接口，可根据医院财务实际需求进行对接

| **类别**                                                     | **接口名称**                                           | **接口服务****标识**                 | **描述**                         |
| ------------------------------------------------------------ | ------------------------------------------------------ | ------------------------------------ | -------------------------------- |
| 电子票据                                                     | [医疗门诊电子票据开具接口](#_医疗门诊电子票据开具接口) | invoiceEBillOutpatient               | 业务系统收费结算成功时，请求接口 |
| [医疗住院电子票据开具接口](#_医疗住院电子票据开具接口)       | invEBillHospitalized                                   |                                      |                                  |
| [电子票据冲红接口](#_电子票据冲红接口)                       | writeOffEBill                                          | 业务系统退费成功时，请求接口         |                                  |
| [根据电子票信息获取电子票据状态接口](#_根据电子票信息获取电子票据状态接口) | getEBillStatesByBillInfo                               | 业务系统需查看电子票据状态，请求接口 |                                  |
| 打印通知                                                     | [打印电子票据接口](#_打印电子票据接口)                 | printElectBill                       | 适用于博思客户端组件             |
| [获取电子票据清单总页数接口](#_获取电子票据清单总页数接口)   | getElectBilllListTotal                                 |                                      |                                  |
| [打印电子票据清单接口](#_打印电子票据清单接口)               | printElectBillList                                     | 适用于博思客户端组件                 |                                  |
| 数据核对                                                     | [总笔数核对接口](#_总笔数核对接口_1)                   | checkTotalData                       |                                  |
| [退费数据核对接口](#_退费数据核对接口_1)                     | checkWriteOffData                                      |                                      |                                  |
| [根据业务时间获取开票信息接口](#_根据业务时间获取开票信息接口) | getBillByBusDate                                       |                                      |                                  |
| [根据开票日期获取总笔数核对接口](#_根据开票日期获取总笔数核对接口) | checkTotalDataByIvcDate                                |                                      |                                  |
| [根据开票日期获取退费数据核对接口](#_根据开票日期获取退费数据核对接口) | checkWriteOffDataByIvcDate                             |                                      |                                  |
| [根据开票日期获取开票信息接口](#_根据开票日期获取开票信息接口) | getBillByIvcDate                                       |                                      |                                  |

## **3.2** ***\*接口对接说明\****

### **3.2.1** ***\*电子票据开具与冲红\****

| **接口类别**   | **使用说明**                                                 |
| -------------- | ------------------------------------------------------------ |
| 电子票据开具类 | 适用于门诊就诊收费结算成功、住院办理出院或者中途结算成功、挂号收费结算成功、体检收费结算成功等业务场景，结算成功之后，请求医疗电子票据管理平台提供的电子票据开具接口，完成电子票据开具 |
| 电子票据冲红类 | 适用于患者申请退款的业务场景，如患者全额退款只需要请求医疗电子票据管理平台提供的电子票据冲红接口，生成一张新的电子票据（红票）；如患者部分退款，需要第一次请求医疗电子票据管理平台电子票据冲红（全额）， 生成一张新的电子票据（红票），在根据实际发生费用，请求医疗电子票据管理平台开具电子票据，生成一张新的电子票据。 |

 

### **3.2.2** ***\*电子票据数据核对\****

| ***\*接口类别\**** | ***\*使用说明\****                                           |
| ------------------ | ------------------------------------------------------------ |
| 数据核对类         | 适用于业务系统进行日终（年月度）对账，提供总数核对、业务时间范围内的开票明细数据核对。核对步骤：先进行总数核对，如总数核对存在差异，在进行开票明细数据核对。 |

 

### **3.2.3** ***\*电子票据查询与库存信息\****

| ***\*接口类别\**** | ***\*使用说明\****                                           |
| ------------------ | ------------------------------------------------------------ |
| 电子票据查询类     | 业务系统需要以电子票据信息，获取电子票据明细、查看电子票据H5页面地址进行展示；如患者就诊结算后，事后需要电子票据告知单，业务系统请求医疗电子票据管理平 台提供的获取电子票据告知单内容接口，获取告知单内容，进行补打告知单。 |

 

 

# **4** ***\*接口描述\****

## **4.1** ***\*电子票据\****

### **4.1.1** ***\*电子票据开具接口\****

业务系统提供开具电子票据信息数据，向医疗电子票据管理平台发起电子票据开具请求，生成电子票据，并返回电子票据相关信息和告知单信息；

主要可以分为门诊、住院、挂号、门特、体检等业务电子票据开具接口。

#### **4.1.1.1** ***\*医疗门诊电子票据开具接口\****

##### 4.1.1.1.1 **服务标识 invoiceEBillOutpatient**

##### 4.1.1.1.2 **服务版本号**

请求参数中version=1.0

##### 4.1.1.1.3 **请求业务参数**

注：就诊信息、费用类别，根据实际医疗票据样式可进行调整

| 类别                   | 参数                                 | 描述             | 类型      | 长度 | 必填                                                         | 补充                                                         |
| ---------------------- | ------------------------------------ | ---------------- | --------- | ---- | ------------------------------------------------------------ | ------------------------------------------------------------ |
| 票据信息               | busNo                                | 业务流水号       | String    | 50   | 是                                                           | 单位内部唯一                                                 |
| busType                | 业务标识                             | String           | 20        | 是   | 直接填写业务系统内部编码值，由医疗平台配置对照，例如：[附录5 业务标识列表](#_附录5：业务类型列表)值：02，标识门诊 |                                                              |
| payer                  | 患者姓名                             | String           | 100       | 是   |                                                              |                                                              |
| busDateTime            | 业务发生时间                         | String           | 17        | 是   | 格式：yyyyMMddHHmmssSSS                                      |                                                              |
| placeCode              | 开票点编码                           | String           | 50        | 是   | 直接填写业务系统内部编码值，由医疗平台配置对照               |                                                              |
| payee                  | 收费员                               | String           | 50        | 是   |                                                              |                                                              |
| author                 | 票据编制人                           | String           | 100       | 是   | 如收费员与开票人员为同一人，则值与payee相同                  |                                                              |
| checker                | 票据复核人                           | String           | 100       | 否   |                                                              |                                                              |
| totalAmt               | 开票总金额                           | Number           | 14,2      | 是   |                                                              |                                                              |
| remark                 | 备注                                 | String           | 200       | 否   |                                                              |                                                              |
| 缴费                   | alipayCode                           | 患者支付宝账户   | String    | 100  | 否                                                           | 患者如果使用支付宝结算，可传入患者的支付宝UserID，用于电子票据归集到支付宝发票管家 |
| weChatOrderNo          | 微信支付订单号                       | String           | 100       | 否   | 微信结算时，可传入患者支付成功的订单号，用来发微信服务通知   |                                                              |
| weChatMedTransNo       | 微信医保支付订单号                   | String           | 100       | 否   | 医保患者使用微信医保支付结算时，填写对应的微信医保支付的订单号 |                                                              |
| openID                 | 微信公众号或小程序用户ID             | String           | 60        | 否   |                                                              |                                                              |
| unionPayOrderNo        | 银联支付信息                         | String           | 100       | 否   | 银联结算时，可传入银联查询id                                 |                                                              |
| payOrderInfo           | 支付信息列表                         | JSONArray        | 不限      | 否   | 填写费用支付类型信息，支付类型不在【患者支付宝账户、微信支付订单号、微信医保支付订单号、微信公众号或小程序用户ID、银联支付信息】范围内容的，其它支付类型信息可通过列表方式传入***\*详见A-7,JSON格式列表\**** |                                                              |
| 通知                   | tel                                  | 患者手机号码     | String    | 11   | 否                                                           | 患者手机号（如需要用于电子票归集、电子票据短信通知，必填）   |
| email                  | 患者邮箱地址                         | String           | 100       | 否   | 患者邮箱地址（如需用于电子票归集、电子票据邮箱通知，必填）   |                                                              |
| payerType              | 交款人类型                           | String           | 1         | 是   | 交款人类型：1 个人 2 单位                                    |                                                              |
| idCardType             | 证件类型                             | String           | 20        | 否   | 例如：[附录3：卡类型与证件类型列表](#_附录3：卡类型与证件类型列表) |                                                              |
| idCardNo               | 统一社会信用代码                     | String           | 30        | 否   | 个人患者可填身份证号码                                       |                                                              |
| cardType               | 卡类型                               | String           | 10        | 是   | 直接填写业务系统内部编码值，由医疗平台配置对照例如：[附录3：卡类型与证件类型列表](#_附录3：卡类型与证件类型列表) |                                                              |
| cardNo                 | 卡号                                 | String           | 50        | 是   | 根据卡类型填写                                               |                                                              |
| 就诊信息               | medicalInstitution                   | 医疗机构类型     | String    | 30   | 是                                                           | 按照《医疗机构管理条例实施细则》和《卫生部关于修订<医疗机构管理条例实施细则>第三条有关内容的通知》确定的医疗卫生机构类别 |
| medCareInstitution     | 医保机构编码                         | String           | 60        | 否   | 医保机构的唯一编码                                           |                                                              |
| medCareTypeCode        | 医保类型编码                         | String           | 30        | 是   |                                                              |                                                              |
| medicalCareType        | 医保类型名称                         | String           | 60        | 是   | 取值范围包括职工基本医疗保险、城乡居民基本医疗保险（城镇居民基本医疗保险、新型农村合作医疗保险）和其他医疗保险、非医保等 |                                                              |
| medicalInsuranceID     | 患者医保编号                         | String           | 30        | 否   | 医保结算票必填参保人在医保系统中的唯一标识(医保号)           |                                                              |
| consultationDate       | 就诊日期                             | String           | 10        | 是   | 患者就医时间格式:yyyyMMdd                                    |                                                              |
| patientCategory        | 就诊科室                             | String           | 60        | 是   | 必填                                                         |                                                              |
| patientCategoryCode    | 就诊科室编码                         | String           | 60        | 否   |                                                              |                                                              |
| patientNo              | 患者就诊编号                         | String           | 30        | 是   | 患者每次就诊一次就生成的一个新的编号。（患者登记号）         |                                                              |
| patientId              | 患者唯一ID                           | String           | 50        | 否   | 患者在业务系统中的唯一标识ID，类似身份证号码。               |                                                              |
| sex                    | 性别                                 | String           | 2         | 否   | 如填值为：男、女                                             |                                                              |
| age                    | 年龄                                 | String           | 10        | 否   |                                                              |                                                              |
| caseNumber             | 病历号                               | String           | 50        | 是   | 必填                                                         |                                                              |
| ICD                    | 疾病编码                             | String           | 30        | 否   |                                                              |                                                              |
| specialDiseasesName    | 特殊病种名称                         | String           | 100       | 否   |                                                              |                                                              |
| 费用                   | accountPay                           | 个人账户支付     | Number    | 14,2 | 是                                                           | 按政策规定用个人账户支付参保人的医疗费用（含基本医疗保险目录范围内和目录范围外的费用）；如无金额，填写0 |
| payMentVoucher         | 预交金凭证消费扣款列表               | JSONArray        | 不限      | 否   | 结算开具电子票据时，传入消费扣款对应预交金凭证信息***\*详见A-6,JSON格式列表\**** |                                                              |
| fundPay                | 医保统筹基金支付                     | Number           | 14,2      | 是   | 患者本次就医所发生的医疗费用中按规定由基本医疗保险统筹基金支付的金额；如无金额，填写0 |                                                              |
| otherfundPay           | 其它医保支付                         | Number           | 14,2      | 是   | 患者本次就医所发生的医疗费用中按规定由大病保险、医疗救助、公务员医疗补助、大额补充、企业补充等基金或资金支付的金额；如无金额，填写0 |                                                              |
| ownPay                 | 自费金额                             | Number           | 14,2      | 是   | 患者本次就医所发生的医疗费用中按照有关规定不属于基本医疗保险目录范围而全部由个人支付的费用；如无金额，填写0 |                                                              |
| selfConceitedAmt       | 个人自负                             | Number           | 14,2      | 是   | 医保患者起付标准内个人支付费用；如无金额，填写0              |                                                              |
| selfPayAmt             | 个人自付                             | Number           | 14,2      | 是   | 患者本次就医所发生的医疗费用中由个人负担的属于基本医疗保险目录范围内自付部分的金额；开展按病种、病组、床日等打包付费方式且由患者定额付费的费用。该项为个人所得税大病医疗专项附加扣除信；息项如无金额，填写0 |                                                              |
| selfCashPay            | 个人现金支付                         | Number           | 14,2      | 是   | 个人通过现金、银行卡、微信、支付宝等渠道支付的金额；如无金额，填写0 |                                                              |
| cashPay                | 预缴金额                             | Number           | 14,2      | 否   |                                                              |                                                              |
| cashRecharge           | 补缴金额                             | Number           | 14,2      | 否   |                                                              |                                                              |
| cashRefund             | 退费金额                             | Number           | 14,2      | 否   |                                                              |                                                              |
| ownAcBalance           | 个人账户余额                         | Number           | 14,2      | 否   |                                                              |                                                              |
| reimbursementAmt       | 报销总金额                           | Number           | 14,2      | 否   | 医保结算后返回的总金额                                       |                                                              |
| balancedNumber         | 医保结算号                           | String           | 100       | 否   | 医保结算票据必填HIS和医保实时结算时，医保生成的唯一业务流水号 |                                                              |
| chargeDate             | 交费日期                             | String           | 10        | 否   | 交费时间格式:yyyyMMdd                                        |                                                              |
| otherInfo              | 其它扩展信息列表                     | JSONArray        | 不限      | 是   | 填写信息需要在电子票据上单独显示的其它扩展信息（未知信息）***\*详见A-3,JSON格式列表\**** |                                                              |
| otherMedicalList       | 其它医保信息列表                     | JSONArray        | 不限      | 否   | 填写其它未知医保信息（在电子票据上以内容拼接方式显示）***\*详见A-4,JSON格式列表\**** |                                                              |
| payChannelDetail       | 交费渠道列表                         | JSONArray        | 不限      | 是   | 直接填写业务系统内部编码值，由医疗平台配置对照如：[附录4交费渠道列表](#_附录4：缴款渠道列表)***\*详见A-5,JSON格式列表\**** |                                                              |
| 医保结算信息           | medicalInstitutionCode               | 定点医疗机构编码 | String    | 12   | 是                                                           | 必填                                                         |
| medicalInstitutionName | 定点医疗机构名称                     | String           | 20        | 是   | 必填                                                         |                                                              |
| diagnosticCode         | 主要诊断代码                         | String           | 50        | 是   | 必填                                                         |                                                              |
| diagnosticName         | 主要诊断名称                         | String           | 200       | 是   | 必填                                                         |                                                              |
| scdDiagCode            | 次要诊断代码                         | String           | 30        | 否   |                                                              |                                                              |
| scdDiagName            | 次要诊断名称                         | String           | 255       | 否   |                                                              |                                                              |
| supninsCode            | 监管机构代码                         | String           | 6         | 否   |                                                              |                                                              |
| policyRangeAmount      | 符合政策范围金额                     | Number           | 16,2      | 是   | 医保结算票据、自费票据均必填                                 |                                                              |
| insuranceType          | 险种类型                             | String           | 6         | 否   | 医保结算票必填可参考[附录13险种类型列表](#_附录13：医保类型) |                                                              |
| medCareJoinAreaCode    | 参保地医保区划                       | String           | 6         | 否   | 医保结算票必填                                               |                                                              |
| medCareAreaCode        | 就医地医保行政区划码                 | String           | 20        | 是   | 医保自费必须填写                                             |                                                              |
| medinsSetlId           | 医保结算ID                           | String           | 30        | 否   | 医保结算票必填                                               |                                                              |
| fixMedInsMdtrtId       | 医疗机构就诊ID                       | String           | 50        | 否   | 全自费时必填                                                 |                                                              |
| medinsSetlTime         | 医保结算时间                         | String           | 17        | 否   | yyyyMMddHHmmssSSS医保结算票必填                              |                                                              |
| 医保结算信息           | fullSelfpaymentAmount                | 全自费金额       | Number    | 14,2 | 是                                                           | 医保结算票据、自费票据均必填                                 |
| overLimitAmount        | 超限价金额                           | Number           | 14,2      | 是   | 医保结算票据、自费票据均必填                                 |                                                              |
| preSelfpaymentAmount   | 先行自付金额                         | Number           | 14,2      | 是   | 医保结算票据、自费票据均必填                                 |                                                              |
| actualPaymentLine      | 实际支付起付线                       | Number           | 14,2      | 否   | 医保结算票据必填                                             |                                                              |
| basicMBFPaidRatio      | 基本医疗保险统筹基金支付比例         | Number           | 3,2       | 否   | 医保结算票据必填                                             |                                                              |
| staffBasicMBFPaid      | 职工基本医疗保险统筹基金支付         | Number           | 14,2      | 否   | 医保结算票据必填                                             |                                                              |
| staffLargeMBFPaid      | 职工大额医疗费用补充保险基金支付     | Number           | 14,2      | 否   | 医保结算票据必填                                             |                                                              |
| residentMBFPaid        | 城乡居民医疗保险统筹基金支付         | Number           | 14,2      | 否   | 医保结算票据必填                                             |                                                              |
| residentLargeMBFPaid   | 城乡居民大额医疗费用补充保险基金支付 | Number           | 14,2      | 否   | 医保结算票据必填                                             |                                                              |
| civilServantMBFPaid    | 公务员医疗补助基金支付               | Number           | 14,2      | 否   | 医保结算票据必填                                             |                                                              |
| enterpriseMBFPaid      | 企业补充医疗保险基金支付             | Number           | 14,2      | 否   | 医保结算票据必填                                             |                                                              |
| rescueMBFPaid          | 医疗救助基金支付                     | Number           | 14,2      | 否   | 医保结算票据必填                                             |                                                              |
| accountMulaidPaid      | 个人账户共计支付                     | Number           | 14,2      | 否   | 医保结算票据必填                                             |                                                              |
| diseaseCode            | 病种编码                             | String           | 50        | 否   | DRG编码，医保结算、自费均必填                                |                                                              |
| diseaseName            | 病种名称                             | String           | 200       | 否   | DRG名称，医保结算、自费均必填                                |                                                              |
| 医保结算信息           | personnelType                        | 人员类型         | String    | 50   | 否                                                           | 1-职工退休；2-职工在职3-公务员在职4-公务员退休5-灵活就业人员6-退休人员等 |
| hospitalizedType       | 住院类别                             | String           | 200       | 否   | 1-普通住院2-生育住院3-新冠住院                               |                                                              |
| hospitalSharing        | 医院分担                             | Number           | 14,2      | 否   | 患者住院费用由医院承担部分                                   |                                                              |
| 其它                   | eBillRelateNo                        | 业务票据关联号   | String    | 32   | 否                                                           | 如一笔业务数据需要开具N张电子票据，则N张电子票对应该值保持一致，用于后期关联查询 |
| isArrears              | 是否可流通                           | String           | 1         | 是   | 0-否、1-是（如欠费情况根据医院业务要求该票据是否可流通）     |                                                              |
| arrearsReason          | 不可流通原因                         | String           | 200       | 否   | isArrears=0，填写不可流通的原因                              |                                                              |
| 项目                   | chargeDetail                         | 收费项目明细     | JSONArray | 不限 | 是                                                           | ***\*详见A-1,JSON格式列表\****                               |
| listDetail             | 清单项目明细                         | JSONArray        | 不限      | 是   | ***\*详见A-2,JSON格式列表\****                               |                                                              |

***\*A-1 收费项目明细chargeDetail->JSONObject类型\****

| 参数       | 描述         | 类型    | 长度 | 必填 | 补充                                                 |
| ---------- | ------------ | ------- | ---- | ---- | ---------------------------------------------------- |
| sortNo     | 序号         | Integer | 不限 | 是   | 默认从1开始，每个收费项目序号值递增1，本次不允许重复 |
| chargeCode | 收费项目代码 | String  | 50   | 是   | 填写业务系统内部编码值，由医疗平台配置对照           |
| chargeName | 收费项目名称 | String  | 100  | 是   | 填写业务系统内部项目名称                             |
| unit       | 计量单位     | String  | 20   | 否   |                                                      |
| std        | 收费标准     | Number  | 14,2 | 是   |                                                      |
| number     | 数量         | Number  | 14,2 | 是   |                                                      |
| amt        | 金额         | Number  | 14,2 | 是   |                                                      |
| selfAmt    | 自费金额     | Number  | 14,2 | 是   | 如无金额，填写0                                      |
| remark     | 备注         | String  | 200  | 否   |                                                      |

***\*A-2 清单项目明细listDetail->JSONObject类型\****

| 参数                      | 描述               | 类型             | 长度          | 必填         | 补充                                                         |
| ------------------------- | ------------------ | ---------------- | ------------- | ------------ | ------------------------------------------------------------ |
| listDetailNo              | 明细流水号         | String           | 60            | 否           | 明细流水号                                                   |
| chargeCode                | 收费项目代码       | String           | 50            | 是           | 填写业务系统内部编码值，由医疗平台配置对照,如：床位费、检查费如填写，值必须在收费项目列表中存在 |
| chargeName                | 收费项目名称       | String           | 100           | 是           |                                                              |
| prescribeCode             | 处方编码           | String           | 60            | 否           |                                                              |
| listTypeCode              | 药品类别编码       | String           | 50            | 否           | 如药品分类编码01，有则填写                                   |
| listTypeName              | 药品类别名称       | String           | 50            | 否           | 如药品分类名称，抗生素类抗感染药物                           |
| ***\*code\****            | ***\*编码\****     | ***\*String\**** | ***\*50\****  | ***\*是\**** | ***\*如药品编码\*******\*。（\*******\*传\*******\*医保药品\*******\*贯标\*******\*编\*******\*码\*******\*）\**** |
| ***\*name\****            | ***\*药品名称\**** | ***\*String\**** | ***\*100\**** | ***\*是\**** | ***\*如药品名称，器材名称等（\*******\*传\*******\*医保药品\*******\*贯标名称\*******\*）\*******\*）\**** |
| form                      | 剂型               | String           | 50            | 否           |                                                              |
| specification             | 规格               | String           | 50            | 否           |                                                              |
| unit                      | 计量单位           | String           | 20            | 是           |                                                              |
| std                       | 单价               | Number           | 14,4          | 是           |                                                              |
| number                    | 数量               | Number           | 14,2          | 是           |                                                              |
| amt                       | 金额               | Number           | 14,4          | 是           |                                                              |
| selfAmt                   | 自费金额           | Number           | 14,4          | 是           | 如无金额，填写0                                              |
| receivableAmt             | 应收费用           | Number           | 14,4          | 否           |                                                              |
| medicalCareType           | 医保药品分类       | String           | 1             | 否           | 1：无自负/甲2：有自负/乙3：全自负/丙                         |
| medCareItemType           | 医保项目类型       | String           | 100           | 否           |                                                              |
| medReimburseRate          | 医保报销比例       | Number           | 3,2           | 否           |                                                              |
| remark                    | 备注               | String           | 200           | 否           |                                                              |
| sortNo                    | 序号               | Integer          | 不限          | 否           | 序号                                                         |
| chrgtype                  | 费用类型           | String           | 50            | 否           |                                                              |
| payDate                   | 费款所属期         | String           | 20            | 是           | 格式：yyyyMMdd;                                              |
| medCareItemCode           | 医保项目编码       | String           | 100           | 是           | 属于国家医保目录内的无论是否医保结算都必须填写，影响报销。   |
| medCareItemName           | 医保项目名称       | String           | 200           | 是           | 属于国家医保目录内的无论是否医保结算都必须填写，影响报销。   |
| medicalInsuranceCostClass | 医保费用等级       | String           | 30            | 是           | 医保结算票据、自费票据均必填（01/02/03）                     |
| itemSelfpaymentRatio      | 收费明细项目自付比 | Number           | 14,2          | 是           | 医保结算票据、自费票据均必填                                 |
| medicalInsurancePayStd    | 医保支付标准       | Number           | 14,2          | 是           | 医保结算票据、自费票据均必填                                 |

***\*A-3 其它扩展信息列表otherInfo->JSONObject类型\****

| 参数      | 描述         | 类型    | 长度 | 必填 | 补充                                             |
| --------- | ------------ | ------- | ---- | ---- | ------------------------------------------------ |
| infoNo    | 序号         | Integer | 不限 | 是   | 默认从1开始，每项数据序号值递增1，本次不允许重复 |
| infoName  | 扩展信息名称 | String  | 100  | 是   |                                                  |
| infoValue | 扩展信息值   | String  | 500  | 是   |                                                  |

***\*A-4 其它医保信息列表otherMedicalList->JSONObject类型\****

| 参数      | 描述         | 类型    | 长度 | 必填 | 补充                                                         |
| --------- | ------------ | ------- | ---- | ---- | ------------------------------------------------------------ |
| infoNo    | 序号         | Integer | 不限 | 是   | 默认从1开始，每项数据序号值递增1，本次不允许重复             |
| infoName  | 医保信息名称 | String  | 100  | 是   | 如费用报销类型编码，可参考[附录7医保报销类型列表](#_附录7：医保报销类型列表) |
| infoValue | 医保信息值   | String  | 100  | 是   | 如费用报销金额                                               |
| infoOther | 医保其它信息 | String  | 100  | 否   | 如医保报销比例。                                             |

***\*A-5 交费渠道列表 payChannelDetail->JSONObject类型\****

| 参数            | 描述         | 类型   | 长度 | 必填 | 补充 |
| --------------- | ------------ | ------ | ---- | ---- | ---- |
| payChannelCode  | 交费渠道编码 | String | 10   | 是   |      |
| payChannelValue | 交费渠道金额 | Number | 14,2 | 是   |      |

***\*A-6 预交金凭证消费扣款列表 payMentVoucher->JSONObject类型\****

| 参数             | 描述           | 类型   | 长度 | 必填 | 补充 |
| ---------------- | -------------- | ------ | ---- | ---- | ---- |
| voucherBatchCode | 预交金凭证代码 | String | 50   | 是   |      |
| voucherNo        | 预交金凭证号码 | String | 20   | 是   |      |

***\*A-7 支付信息列表 payOrderInfo->JSONObject类型\****

| 参数          | 描述         | 类型   | 长度 | 必填 | 补充                                                   |
| ------------- | ------------ | ------ | ---- | ---- | ------------------------------------------------------ |
| payOrderCode  | 支付类型编码 | String | 10   | 是   | 参考[附录8支付类型编码列表](#_附录7：医保报销类型列表) |
| payOrderValue | 支付类型值   | String | 100  | 是   |                                                        |

##### 4.1.1.1.4 **返回结果参数**

| 参数    | 描述         | 类型   | 长度 | 必填 | 补充                                           |
| ------- | ------------ | ------ | ---- | ---- | ---------------------------------------------- |
| result  | 返回结果标识 | String | 10   | 是   | S0000                                          |
| message | 返回结果内容 | String | 不限 | 是   | ***\*详见 B-1，JSON格式，\*******\*BASE64\**** |

***\*B-1 返回结果内容message，响应业务参数->JSONObject类型\****

| 参数          | 描述                   | 类型   | 长度 | 必填 | 补充                                                  |
| ------------- | ---------------------- | ------ | ---- | ---- | ----------------------------------------------------- |
| billBatchCode | 电子票据代码           | String | 50   | 是   |                                                       |
| billNo        | 电子票据号码           | String | 20   | 是   |                                                       |
| random        | 电子校验码             | String | 20   | 是   |                                                       |
| createTime    | 电子票据生成时间       | String | 17   | 是   | 创建时间：时间格式精确到毫秒yyyyMMddHHmmssSSS         |
| billQRCode    | 电子票据二维码图片数据 | String | 不限 | 是   | 该值已Base64编码，解析时需要Base64解码，图片格式为PNG |
| pictureUrl    | 电子票据H5页面URL      | String | 不限 | 是   |                                                       |
| pictureNetUrl | 电子票据外网H5页面URL  | String | 不限 | 否   | 按需配置                                              |
| wxCardUrl     | 微信插卡URL            | String | 不限 | 否   |                                                       |

 

#### **4.1.1.2** ***\*医疗住院电子票据开具接口\****

##### 4.1.1.2.1 **服务标识 invEBillHospitalized**

##### 4.1.1.2.2 **服务版本号**

请求参数中version=1.0

##### 4.1.1.2.3 **请求业务参数**

注：就诊信息、费用类别，根据实际医疗票据样式可进行调整；

| 类别                   | 参数                                 | 描述                   | 类型      | 长度 | 必填                                                         | 补充                                                         |
| ---------------------- | ------------------------------------ | ---------------------- | --------- | ---- | ------------------------------------------------------------ | ------------------------------------------------------------ |
| 票据信息               | busNo                                | 业务流水号             | String    | 50   | 是                                                           | 单位内部唯一                                                 |
| busType                | 业务标识                             | String                 | 20        | 是   | 直接填写业务系统内部编码值，由医疗平台配置对照，例如：[附录5 业务标识列表](#_附录5：业务类型列表)值：01，标识住院 |                                                              |
| payer                  | 患者姓名                             | String                 | 100       | 是   |                                                              |                                                              |
| busDateTime            | 业务发生时间                         | String                 | 17        | 是   | 格式：yyyyMMddHHmmssSSS                                      |                                                              |
| placeCode              | 开票点编码                           | String                 | 50        | 是   | 直接填写业务系统内部编码值，由医疗平台配置对照               |                                                              |
| payee                  | 收费员                               | String                 | 50        | 是   |                                                              |                                                              |
| author                 | 票据编制人                           | String                 | 100       | 是   | 如收费员与开票人员为同一人，则值与payee相同                  |                                                              |
| checker                | 票据复核人                           | String                 | 100       | 是   |                                                              |                                                              |
| totalAmt               | 开票总金额                           | Number                 | 14,2      | 是   |                                                              |                                                              |
| remark                 | 备注                                 | String                 | 200       | 否   |                                                              |                                                              |
| 缴费                   | alipayCode                           | 患者支付宝账户         | String    | 100  | 否                                                           | 患者如果使用支付宝结算，可传入患者的支付宝UserID，用于电子票据归集到支付宝发票管家 |
| weChatOrderNo          | 微信支付订单号                       | String                 | 100       | 否   | 微信结算时，可传入患者支付成功的订单号，用来发微信服务通知   |                                                              |
| weChatMedTransNo       | 微信医保支付订单号                   | String                 | 100       | 否   | 医保患者使用微信医保支付结算时，填写对应的微信医保支付的订单号 |                                                              |
| openID                 | 微信公众号或小程序用户ID             | String                 | 60        | 否   |                                                              |                                                              |
| unionPayOrderNo        | 银联支付信息                         | String                 | 100       | 否   | 银联结算时，可传入银联查询id                                 |                                                              |
| payOrderInfo           | 支付信息列表                         | JSONArray              | 不限      | 否   | 填写费用支付类型信息，支付类型不在【患者支付宝账户、微信支付订单号、微信医保支付订单号、微信公众号或小程序用户ID、银联支付信息】中的，其它支付类型信息可通过列表方式传入***\*详见A-7,JSON格式列表\**** |                                                              |
| 通知                   | tel                                  | 患者手机号码           | String    | 11   | 否                                                           | 患者手机号（如需要用于电子票归集、电子票据短信通知，必填）   |
| email                  | 患者邮箱地址                         | String                 | 100       | 否   | 患者邮箱地址（如需用于电子票归集、电子票据邮箱通知，必填）   |                                                              |
| payerType              | 交款人类型                           | String                 | 1         | 是   | 交款人类型：1 个人 2 单位                                    |                                                              |
| idCardType             | 证件类型                             | String                 | 20        | 否   | [附录3：卡类型与证件类型列表](#_附录3：卡类型与证件类型列表) |                                                              |
| idCardNo               | 统一社会信用代码                     | String                 | 30        | 否   | 个人患者可填身份证号码                                       |                                                              |
| cardType               | 卡类型                               | String                 | 10        | 是   | 直接填写业务系统内部编码值，由医疗平台配置对照例如：[附录3：卡类型与证件类型列表](#_附录3：卡类型与证件类型列表) |                                                              |
| cardNo                 | 卡号                                 | String                 | 50        | 是   | 根据卡类型填写                                               |                                                              |
| 就诊信息               | medicalInstitution                   | 医疗机构类型           | String    | 30   | 是                                                           | 按照《医疗机构管理条例实施细则》和《卫生部关于修订<医疗机构管理条例实施细则>第三条有关内容的通知》确定的医疗卫生机构类别 |
| medCareInstitution     | 医保机构编码                         | String                 | 60        | 否   | 医保机构的唯一编码                                           |                                                              |
| medCareTypeCode        | 医保类型编码                         | String                 | 30        | 是   |                                                              |                                                              |
| medicalCareType        | 医保类型名称                         | String                 | 60        | 是   | 由城镇职工基本医疗保险、城镇居民基本医疗保险、新型农村合作医疗、其它医疗保险等构成 |                                                              |
| medicalInsuranceID     | 患者医保编号                         | String                 | 30        | 否   | 医保结算票必填参保人在医保系统中的唯一标识(医保号)           |                                                              |
| category               | 入院科室名称                         | String                 | 50        | 是   | 必填                                                         |                                                              |
| categoryCode           | 入院科室编码                         | String                 | 50        | 否   |                                                              |                                                              |
| leaveCategory          | 出院科室名称                         | String                 | 50        | 否   |                                                              |                                                              |
| leaveCategoryCode      | 出院科室编码                         | String                 | 50        | 否   |                                                              |                                                              |
| hospitalNo             | 患者住院号                           | String                 | 20        | 是   | 从入院到出院结束后，整个流程的唯一号                         |                                                              |
| visitNo                | 住院就诊编号                         | String                 | 30        | 是   | 住院期间，存在多次结算，结算后会重新生成一个住院就诊编号，如无就诊编号，可等于患者住院号 |                                                              |
| consultationDate       | 就诊日期                             | String                 | 10        | 是   | 患者就医时间格式:yyyyMMdd                                    |                                                              |
| patientId              | 患者唯一ID                           | String                 | 50        | 否   | 患者在业务系统中的唯一标识ID，类似身份证号码。               |                                                              |
| patientNo              | 患者就诊编号                         | String                 | 30        | 否   | 患者每次就诊一次就生成的一个新的编号。（患者登记号）         |                                                              |
| sex                    | 性别                                 | String                 | 2         | 否   | 如填值为：男、女                                             |                                                              |
| age                    | 年龄                                 | String                 | 10        | 否   |                                                              |                                                              |
| hospitalArea           | 病区                                 | String                 | 60        | 否   |                                                              |                                                              |
| bedNo                  | 床号                                 | String                 | 20        | 否   |                                                              |                                                              |
| caseNumber             | 病历号                               | String                 | 50        | 是   | 必填                                                         |                                                              |
| ICD                    | 疾病编码                             | String                 | 30        | 否   |                                                              |                                                              |
| inHospitalDate         | 住院日期                             | String                 | 10        | 是   | 格式:yyyyMMdd                                                |                                                              |
| outHospitalDate        | 出院日期                             | String                 | 10        | 是   | 格式:yyyyMMdd                                                |                                                              |
| hospitalDays           | 住院天数                             | Number                 | 6,2       | 否   |                                                              |                                                              |
| 费用                   | payMentVoucher                       | 预交金凭证消费扣款列表 | JSONArray | 不限 | 否                                                           | 结算开具电子票据时，传入消费扣款对应预交金凭证信息***\*详见A-6,JSON格式列表\**** |
| accountPay             | 个人账户支付                         | Number                 | 14,2      | 是   | 按政策规定用个人账户支付参保人的医疗费用（含基本医疗保险目录范围内和目录范围外的费用）；如无金额，填写0 |                                                              |
| fundPay                | 医保统筹基金支付                     | Number                 | 14,2      | 是   | 患者本次就医所发生的医疗费用中按规定由基本医疗保险统筹基金支付的金额；如无金额，填写0 |                                                              |
| otherfundPay           | 其它医保支付                         | Number                 | 14,2      | 是   | 患者本次就医所发生的医疗费用中按规定由大病保险、医疗救助、公务员医疗补助、大额补充、企业补充等基金或资金支付的金额；如无金额，填写0 |                                                              |
| ownPay                 | 个人自费金额                         | Number                 | 14,2      | 是   | 患者本次就医所发生的医疗费用中按照有关规定不属于基本医疗保险目录范围而全部由个人支付的费用；如无金额，填写0 |                                                              |
| selfConceitedAmt       | 个人自负                             | Number                 | 14,2      | 是   | 医保患者起付标准内个人支付费用；如无金额，填写0              |                                                              |
| selfPayAmt             | 个人自付                             | Number                 | 14,2      | 是   | 患者本次就医所发生的医疗费用中由个人负担的属于基本医疗保险目录范围内自付部分的金额；开展按病种、病组、床日等打包付费方式且由患者定额付费的费用。该项为个人所得税大病医疗专项附加扣除信；如无金额，填写0 |                                                              |
| selfCashPay            | 个人现金支付                         | Number                 | 14,2      | 是   | 个人通过现金、银行卡、微信、支付宝等渠道支付的金额；如无金额，填写0 |                                                              |
| cashPay                | 预缴金额                             | Number                 | 14,2      | 否   |                                                              |                                                              |
| cashRecharge           | 补缴金额                             | Number                 | 14,2      | 否   |                                                              |                                                              |
| cashRefund             | 退费金额                             | Number                 | 14,2      | 否   |                                                              |                                                              |
| ownAcBalance           | 个人账户余额                         | Number                 | 14,2      | 否   |                                                              |                                                              |
| reimbursementAmt       | 报销总金额                           | Number                 | 14,2      | 否   | 医保结算后返回的总金额                                       |                                                              |
| balancedNumber         | 医保结算号                           | String                 | 100       | 否   | 医保结算票据必填HIS和医保实时结算时，医保生成的唯一业务流水号 |                                                              |
| otherInfo              | 其它扩展信息列表                     | JSONArray              | 不限      | 是   | 填写信息需要在电子票据上单独显示的其它扩展信息（未知信息）***\*详见A-3,JSON格式列表\**** |                                                              |
| otherMedicalList       | 其它医保信息列表                     | JSONArray              | 不限      | 否   | 填写其它未知医保信息（在电子票据上以内容拼接方式显示）***\*详见A-4,JSON格式列表\**** |                                                              |
| payChannelDetail       | 交费渠道列表                         | JSONArray              | 不限      | 是   | 直接填写业务系统内部编码值，由医疗平台配置对照如：[附录4交费渠道列表](#_附录4：缴款渠道列表)***\*详见A-5,JSON格式列表\**** |                                                              |
| 医保结算信息           | medicalInstitutionCode               | 定点医疗机构编码       | String    | 12   | 是                                                           | 必填                                                         |
| medicalInstitutionName | 定点医疗机构名称                     | String                 | 20        | 是   | 必填                                                         |                                                              |
| diagnosticCode         | 主要诊断代码                         | String                 | 50        | 是   | 必填                                                         |                                                              |
| diagnosticName         | 主要诊断名称                         | String                 | 200       | 是   | 必填                                                         |                                                              |
| scdDiagCode            | 次要诊断代码                         | String                 | 30        | 否   |                                                              |                                                              |
| scdDiagName            | 次要诊断名称                         | String                 | 255       | 否   |                                                              |                                                              |
| supninsCode            | 监管机构代码                         | String                 | 6         | 否   |                                                              |                                                              |
| policyRangeAmount      | 符合政策范围金额                     | Number                 | 16,2      | 是   | 医保结算票据、自费票据均必填                                 |                                                              |
| insuranceType          | 险种类型                             | String                 | 6         | 否   | 医保结算票必填可参考[附录13险种类型列表](#_附录13：医保类型) |                                                              |
| medCareJoinAreaCode    | 参保地医保区划                       | String                 | 6         | 否   | 医保结算票必填                                               |                                                              |
| medCareAreaCode        | 就医地医保行政区划码                 | String                 | 20        | 是   | 必须填写                                                     |                                                              |
| medinsSetlId           | 医保结算ID                           | String                 | 30        | 否   | 医保结算票必填                                               |                                                              |
| fixMedInsMdtrtId       | 医疗机构就诊ID                       | String                 | 50        | 否   | 全自费时必填                                                 |                                                              |
| medinsSetlTime         | 医保结算时间                         | String                 | 17        | 否   | yyyyMMddHHmmssSSS医保结算票必填                              |                                                              |
| 医保结算信息           | fullSelfpaymentAmount                | 全自费金额             | Number    | 14,2 | 是                                                           | 医保结算票据、自费票据均必填                                 |
| overLimitAmount        | 超限价金额                           | Number                 | 14,2      | 是   | 医保结算票据、自费票据均必填                                 |                                                              |
| preSelfpaymentAmount   | 先行自付金额                         | Number                 | 14,2      | 是   | 医保结算票据、自费票据均必填                                 |                                                              |
| actualPaymentLine      | 实际支付起付线                       | Number                 | 14,2      | 否   | 医保结算票据必填                                             |                                                              |
| basicMBFPaidRatio      | 基本医疗保险统筹基金支付比例         | Number                 | 3,2       | 否   | 医保结算票据必填                                             |                                                              |
| staffBasicMBFPaid      | 职工基本医疗保险统筹基金支付         | Number                 | 14,2      | 否   | 医保结算票据必填                                             |                                                              |
| staffLargeMBFPaid      | 职工大额医疗费用补充保险基金支付     | Number                 | 14,2      | 否   | 医保结算票据必填                                             |                                                              |
| residentMBFPaid        | 城乡居民医疗保险统筹基金支付         | Number                 | 14,2      | 否   | 医保结算票据必填                                             |                                                              |
| residentLargeMBFPaid   | 城乡居民大额医疗费用补充保险基金支付 | Number                 | 14,2      | 否   | 医保结算票据必填                                             |                                                              |
| civilServantMBFPaid    | 公务员医疗补助基金支付               | Number                 | 14,2      | 否   | 医保结算票据必填                                             |                                                              |
| enterpriseMBFPaid      | 企业补充医疗保险基金支付             | Number                 | 14,2      | 否   | 医保结算票据必填                                             |                                                              |
| rescueMBFPaid          | 医疗救助基金支付                     | Number                 | 14,2      | 否   | 医保结算票据必填                                             |                                                              |
| accountMulaidPaid      | 个人账户共计支付                     | Number                 | 14,2      | 否   | 医保结算票据必填                                             |                                                              |
| diseaseCode            | 病种编码                             | String                 | 50        | 否   | DRG编码，医保结算、自费均必填                                |                                                              |
| diseaseName            | 病种名称                             | String                 | 200       | 否   | DRG名称，医保结算、自费均必填                                |                                                              |
| 医保结算信息           | personnelType                        | 人员类型               | String    | 50   | 否                                                           | 7-职工退休；8-职工在职9-公务员在职10-公务员退休11-灵活就业人员12-退休人员等 |
| hospitalizedType       | 住院类别                             | String                 | 200       | 否   | 3-普通住院4-生育住院3-新冠住院                               |                                                              |
| hospitalSharing        | 医院分担                             | Number                 | 14,2      | 否   | 患者住院费用由医院承担部分                                   |                                                              |
| 其它                   | eBillRelateNo                        | 业务票据关联号         | String    | 32   | 否                                                           | 如一笔业务数据需要开具N张电子票据，则N张电子票对应该值保持一致，用于后期关联查询 |
| isArrears              | 是否可流通                           | String                 | 1         | 是   | 0-否、1-是（如欠费情况根据医院业务要求该票据是否可流通）     |                                                              |
| arrearsReason          | 不可流通原因                         | String                 | 200       | 否   | isArrears=0，填写不可流通的原因                              |                                                              |
| 项目                   | chargeDetail                         | 收费项目明细           | JSONArray | 不限 | 是                                                           | ***\*详见A-1,JSON格式列表\****                               |
| listDetail             | 清单项目明细                         | JSONArray              | 不限      | 是   | ***\*详见A-2,JSON格式列表\****                               |                                                              |

***\*A-1 收费项目明细chargeDetail->JSONObject类型\****

| 参数       | 描述         | 类型    | 长度 | 必填 | 补充                                                 |
| ---------- | ------------ | ------- | ---- | ---- | ---------------------------------------------------- |
| sortNo     | 序号         | Integer | 不限 | 是   | 默认从1开始，每个收费项目序号值递增1，本次不允许重复 |
| chargeCode | 收费项目代码 | String  | 50   | 是   | 填写业务系统内部编码值，由医疗平台配置对照           |
| chargeName | 收费项目名称 | String  | 100  | 是   | 填写业务系统内部项目名称                             |
| unit       | 计量单位     | String  | 20   | 否   |                                                      |
| std        | 收费标准     | Number  | 14,2 | 是   | 如无标准，填写金额                                   |
| number     | 数量         | Number  | 14,2 | 是   | 如无数量，填写1                                      |
| amt        | 金额         | Number  | 14,2 | 是   | 收费标准*数量                                        |
| selfAmt    | 自费金额     | Number  | 14,2 | 是   | 如无金额，填写0                                      |
| remark     | 备注         | String  | 200  | 否   |                                                      |

***\*A-2 清单项目明细listDetail->JSONObject类型\****

| 参数                      | 描述               | 类型             | 长度          | 必填         | 补充                                                         |
| ------------------------- | ------------------ | ---------------- | ------------- | ------------ | ------------------------------------------------------------ |
| listDetailNo              | 明细流水号         | String           | 60            | 否           | 明细流水号                                                   |
| chargeCode                | 收费项目代码       | String           | 50            | 是           | 填写业务系统内部编码值，由医疗平台配置对照,如：床位费、检查费如填写，值必须在收费项目列表中存在 |
| chargeName                | 收费项目名称       | String           | 100           | 是           |                                                              |
| prescribeCode             | 处方编码           | String           | 60            | 否           |                                                              |
| listTypeCode              | 药品类别编码       | String           | 50            | 否           | 如药品分类编码01，有则填写                                   |
| listTypeName              | 药品类别名称       | String           | 50            | 否           | 如药品分类名称，抗生素类抗感染药物                           |
| ***\*code\****            | ***\*编码\****     | ***\*String\**** | ***\*50\****  | ***\*是\**** | ***\*如药品编码\*******\*（\*******\*传\*******\*医保药品\*******\*贯标\*******\*编\*******\*码\*******\*）\**** |
| ***\*name\****            | ***\*药品名称\**** | ***\*String\**** | ***\*100\**** | ***\*是\**** | ***\*如药品名称，器材名称等\*******\*（\*******\*传\*******\*医保药品\*******\*贯标\*******\*名称）\**** |
| form                      | 剂型               | String           | 50            | 否           |                                                              |
| specification             | 规格               | String           | 50            | 否           |                                                              |
| unit                      | 计量单位           | String           | 20            | 是           |                                                              |
| std                       | 单价               | Number           | 14,4          | 是           |                                                              |
| number                    | 数量               | Number           | 14,2          | 是           |                                                              |
| amt                       | 金额               | Number           | 14,4          | 是           |                                                              |
| selfAmt                   | 自费金额           | Number           | 14,4          | 是           | 如无金额，填写0                                              |
| receivableAmt             | 应收费用           | Number           | 14,6          | 否           |                                                              |
| medicalCareType           | 医保药品分类       | String           | 1             | 否           | 1：无自负/甲2：有自负/乙3：全自负/丙                         |
| medCareItemType           | 医保项目类型       | String           | 100           | 否           |                                                              |
| medReimburseRate          | 医保报销比例       | Number           | 3,2           | 否           |                                                              |
| remark                    | 备注               | String           | 200           | 否           |                                                              |
| sortNo                    | 序号               | Integer          | 不限          | 否           | 序号                                                         |
| chrgtype                  | 费用类型           | String           | 50            | 否           |                                                              |
| payDate                   | 费款所属期         | String           | 20            | 是           | 格式：yyyyMMdd                                               |
| medCareItemCode           | 医保项目编码       | String           | 100           | 是           | 属于国家医保目录内的无论是否医保结算都必须填写，影响报销。   |
| medCareItemName           | 医保项目名称       | String           | 200           | 是           | 属于国家医保目录内的无论是否医保结算都必须填写，影响报销。   |
| medicalInsuranceCostClass | 医保费用等级       | String           | 30            | 是           | 医保结算票据、自费票据均必填                                 |
| itemSelfpaymentRatio      | 收费明细项目自付比 | Number           | 14,2          | 是           | 医保结算票据、自费票据均必填                                 |
| medicalInsurancePayStd    | 医保支付标准       | Number           | 14,2          | 是           | 医保结算票据、自费票据均必填                                 |

***\*A-3 其它扩展信息列表otherInfo->JSONObject类型\****

| 参数      | 描述         | 类型    | 长度 | 必填 | 补充                                             |
| --------- | ------------ | ------- | ---- | ---- | ------------------------------------------------ |
| infoNo    | 序号         | Integer | 不限 | 是   | 默认从1开始，每项数据序号值递增1，本次不允许重复 |
| infoName  | 扩展信息名称 | String  | 100  | 是   |                                                  |
| infoValue | 扩展信息值   | String  | 500  | 是   |                                                  |

***\*A-4 其它医保信息列表otherMedicalList->JSONObject类型\****

| 参数      | 描述         | 类型    | 长度 | 必填 | 补充                                                         |
| --------- | ------------ | ------- | ---- | ---- | ------------------------------------------------------------ |
| infoNo    | 序号         | Integer | 不限 | 是   | 默认从1开始，每项数据序号值递增1，本次不允许重复             |
| infoName  | 医保信息名称 | String  | 100  | 是   | 如费用报销类型编码，可参考[附录7医保报销类型列表](#_附录7：医保报销类型列表) |
| infoValue | 医保信息值   | String  | 100  | 是   | 如费用报销金额                                               |
| infoOther | 医保其它信息 | String  | 100  | 否   | 如医保报销比例。                                             |

***\*A-5 交费渠道列表 payChannelDetail->JSONObject类型\****

| 参数            | 描述         | 类型   | 长度 | 必填 | 补充 |
| --------------- | ------------ | ------ | ---- | ---- | ---- |
| payChannelCode  | 交费渠道编码 | String | 10   | 是   |      |
| payChannelValue | 交费渠道金额 | Number | 14,2 | 是   |      |

***\*A-6 预交金凭证消费扣款列表 payMentVoucher->JSONObject类型\****

| 参数             | 描述           | 类型   | 长度 | 必填 | 补充 |
| ---------------- | -------------- | ------ | ---- | ---- | ---- |
| voucherBatchCode | 预交金凭证代码 | String | 50   | 是   |      |
| voucherNo        | 预交金凭证号码 | String | 20   | 是   |      |

***\*A-7 支付信息列表 payOrderInfo->JSONObject类型\****

| 参数          | 描述         | 类型   | 长度 | 必填 | 补充                                                   |
| ------------- | ------------ | ------ | ---- | ---- | ------------------------------------------------------ |
| payOrderCode  | 支付类型编码 | String | 10   | 是   | 参考[附录8支付类型编码列表](#_附录7：医保报销类型列表) |
| payOrderValue | 支付类型值   | String | 100  | 是   |                                                        |

##### 4.1.1.2.4 **返回结果参数**

| 参数    | 描述         | 类型   | 长度 | 必填 | 补充                                           |
| ------- | ------------ | ------ | ---- | ---- | ---------------------------------------------- |
| result  | 返回结果标识 | String | 10   | 是   | S0000                                          |
| message | 返回结果内容 | String | 不限 | 是   | ***\*详见 B-1，JSON格式，\*******\*BASE64\**** |

***\*B-1 返回结果内容message，响应业务参数->JSONObject类型\****

| 参数          | 描述                   | 类型   | 长度 | 必填 | 补充                                                  |
| ------------- | ---------------------- | ------ | ---- | ---- | ----------------------------------------------------- |
| billBatchCode | 电子票据代码           | String | 50   | 是   |                                                       |
| billNo        | 电子票据号码           | String | 20   | 是   |                                                       |
| random        | 电子校验码             | String | 20   | 是   |                                                       |
| createTime    | 电子票据生成时间       | String | 17   | 是   | 创建时间：时间格式精确到毫秒yyyyMMddHHmmssSSS         |
| billQRCode    | 电子票据二维码图片数据 | String | 不限 | 是   | 该值已Base64编码，解析时需要Base64解码,图片格式为:PNG |
| pictureUrl    | 电子票据H5页面URL      | String | 不限 | 是   |                                                       |
| pictureNetUrl | 电子票据外网H5页面URL  | String | 不限 | 否   | 按需配置                                              |
| wxCardUrl     | 微信插卡URL            | String | 不限 | 否   |                                                       |

 

### **4.1.2** ***\*电子票据冲红接口\****

业务系统根据电子票据信息，向医疗电子票据管理平台发起已使用票据冲红请求。

#### **4.1.2.1** ***\*服务标识\**** ***\*writeOffEBill\****

#### **4.1.2.2** ***\*服务版本号\****

请求参数中version=1.0

#### **4.1.2.3** ***\*请求业务参数\****

| 属性          | 描述         | 类型   | 长度 | 必填 | 补充                                           |
| ------------- | ------------ | ------ | ---- | ---- | ---------------------------------------------- |
| billBatchCode | 电子票据代码 | String | 50   | 是   |                                                |
| billNo        | 电子票据号码 | String | 20   | 是   |                                                |
| reason        | 冲红原因     | String | 200  | 是   |                                                |
| operator      | 经办人       | String | 60   | 是   |                                                |
| busDateTime   | 业务发生时间 | String | 17   | 是   | yyyyMMddHHmmssSSS                              |
| placeCode     | 开票点编码   | String | 50   | 是   | 直接填写业务系统内部编码值，由医疗平台配置对照 |

#### **4.1.2.4** ***\*返回结果参数\****

| 参数    | 描述         | 类型   | 长度 | 必填 | 补充                                           |
| ------- | ------------ | ------ | ---- | ---- | ---------------------------------------------- |
| result  | 返回结果标识 | String | 10   | 是   | S0000                                          |
| message | 返回结果内容 | String | 不限 | 是   | ***\*详见 B-1，JSON格式，\*******\*BASE64\**** |

***\*B\*******\*-\*******\*1\**** ***\*返回\*******\*结果内容message\*******\*，响应\*******\*业务参数\****

| 属性                  | 描述                      | 类型   | 长度 | 必填 | 补充                                   |
| --------------------- | ------------------------- | ------ | ---- | ---- | -------------------------------------- |
| eScarletBillBatchCode | 电子红票票据代码          | String | 20   | 是   |                                        |
| eScarletBillNo        | 电子红票票据号码          | String | 20   | 是   |                                        |
| eScarletRandom        | 电子红票校验码            | String | 20   | 是   |                                        |
| createTime            | 电子红票生成时间          | String | 17   | 是   | yyyyMMddHHmmssSSS                      |
| billQRCode            | 电子票据二维码图片数据    | String | 不限 | 是   | 该值已Base64编码，解析时需要Base64解码 |
| pictureUrl            | 电子票据H5页面URL         | String | 不限 | 是   |                                        |
| pictureNetUrl         | 电子票据外网H5页面URL地址 | String | 不限 | 否   | 按需配置                               |

#### **4.1.2.5** ***\*参数示例\****

l 请求参数

{

"billBatchCode": "电子票据代码",

"billNo": "电子票据号码",

"reason": "冲红原因",

 "operator": "经办人",

 "busDateTime": "业务发生时间",

 "placeCode": "收费员编码"

}

 

l 返回参数

成功

{

 "result": "S0000",

 "message": {

  "eScarletBillBatchCode": "电子红票票据代码",

  "eScarletBillNo": "电子红票票据号码",

  "eScarletRandom": "电子红票校验码",

  "createTime": "电子红票生成时间",

  "billQRCode": "电子红票二维码图片数据",

"pictureUrl": "电子票据H5页面URL",

"pictureNetUrl": "电子票据外网H5页面URL地址"

 }

}

失败

{

"result": "E0002",

 "message": "错误"

}

### **4.1.3** ***\*根据\*******\*电子\*******\*票\*******\*信息\*******\*获取电子\*******\*票据状态接口\****

业务系统根据电子票据信息向医疗电子票据管理平台发起获取电子票据状态请求,获取已开电子票据的实际状态信息。

#### **4.1.3.1** ***\*服务标识\**** ***\*getEBillStatesByBillInfo\****

#### **4.1.3.2** ***\*服务版本号\****

请求参数中version=1.0

#### **4.1.3.3** ***\*请求业务参数\****

| 属性          | 描述         | 类型   | 长度 | 必填 | 补充 |
| ------------- | ------------ | ------ | ---- | ---- | ---- |
| billBatchCode | 电子票据代码 | String | 50   | 是   |      |
| billNo        | 电子票据号码 | String | 20   | 是   |      |

#### **4.1.3.4** ***\*返回结果参数\****

| 参数    | 描述         | 类型   | 长度 | 必填 | 补充                                         |
| ------- | ------------ | ------ | ---- | ---- | -------------------------------------------- |
| result  | 返回结果标识 | String | 10   | 是   | S0000                                        |
| message | 返回结果内容 | String | 不限 | 是   | ***\*详见 B-1，JSON格式\*******\*BASE64\**** |

***\*B\*******\*-\*******\*1\**** ***\*返回\*******\*结果内容message\*******\*，响应\*******\*业务参数\****

| 参数                 | 描述                 | 类型   | 长度 | 必填 | 补充                                                   |
| -------------------- | -------------------- | ------ | ---- | ---- | ------------------------------------------------------ |
| billName             | 电子票据种类名称     | String | 200  | 是   |                                                        |
| billBatchCode        | 电子票据代码         | String | 50   | 是   |                                                        |
| billNo               | 电子票据号码         | String | 20   | 是   |                                                        |
| random               | 电子校验码           | String | 20   | 是   |                                                        |
| ivcDateTime          | 开票时间             | String | 17   | 是   | yyyyMMddHHmmssSSS                                      |
| state                | 状态                 | String | 1    | 是   | 状态：1正常，2作废                                     |
| isPrtPaper           | 是否打印纸质票据     | String | 1    | 是   | 0未打印，1已打印                                       |
| pBillBatchCode       | 纸质票据代码         | String | 50   | 否   | 如已打印纸质，则有值                                   |
| pBillNo              | 纸质票据号码         | String | 20   | 否   | 如已打印纸质，则有值                                   |
| pCreateTime          | 纸质票据生成时间     | String | 17   | 否   | 如已开红票，有值格式：yyyyMMddHHmmssSSS                |
| pBillBusDate         | 纸质票据业务发生时间 | String | 17   | 否   | 如已开红票，有值格式：yyyyMMddHHmmssSSS                |
| isScarlet            | 是否已开红票         | String | 1    | 是   | 0未开红票，1已开红票                                   |
| scarletBillBatchCode | 电子红票代码         | String | 50   | 否   | 如已开红票，有值                                       |
| scarletBillNo        | 电子红票号码         | String | 20   | 否   | 如已开红票，有值                                       |
| scarletRandom        | 电子红票随机码       | String | 20   | 否   | 如已开红票，有值                                       |
| scarletBillQRCode    | 电子红票二维码图片   | String | 不限 | 否   | 如已开红票，有值该值已Base64编码，解析时需要Base64解码 |
| scarletCreateTime    | 电子红票生成时间     | String | 17   | 否   | 如已开红票，有值格式：yyyyMMddHHmmssSSS                |
| scarletBillBusDate   | 电子红票业务发生时间 | String | 17   | 否   | 如已开红票，有值格式：yyyyMMddHHmmssSSS                |

#### **4.1.3.5** ***\*参数示例\****

l 请求参数

{

"billBatchCode": "电子票据代码",

"billNo": "电子票据号码"

}

l 返回参数

成功

{

 "result": "S0000",

 "message": {

  "billName": "电子票据种类名称",

  "billBatchCode": "电子票据代码",

  "billNo": "电子票据号码",

  "random": "电子票校验码",

  "ivcDateTime": "开票时间",

  "state": "状态",

  "isPrtPaper": "是否打印纸质票据",

  "pBillBatchCode": "纸质票据代码",

  "pBillNo": "纸质票据号码",

  "isScarlet": "是否已开红票",

  "scarletBillBatchCode": "电子红票代码",

  "scarletBillNo": "电子红票号码",

"scarletRandom": "电子红票随机码",

"scarletBillQRCode": "电子红票二维码图片数据"

 }

}

失败

{

"result": "E0002",

 "message": "错误"

}

### **4.1.4** ***\*打印电子票据接口\****

业务系统根据电子票据信息，向医疗电子票据管理平台发起打印电子票据请求，并完成电子票据打印（如：电子票据图片，打印在A4纸上）。

注：仅适用于业务系统调用博思客户端组件（通过JS访问组件或直接访问组件）。

#### **4.1.4.1** ***\*服务标识 p\*******\*rintElectBill\****

#### **4.1.4.2** ***\*服务版本号\****

请求参数中version=1.0

#### **4.1.4.3** ***\*请求业务参数\****

| 参数          | 描述           | 类型   | 长度 | 必填 | 补充 |
| ------------- | -------------- | ------ | ---- | ---- | ---- |
| billBatchCode | 电子票据代码   | String | 50   | 是   |      |
| billNo        | 电子票据号     | String | 20   | 是   |      |
| random        | 电子票据校验码 | String | 20   | 是   |      |

#### **4.1.4.4** ***\*返回结果参数\****

| 参数    | 描述         | 类型   | 长度 | 必填 | 补充             |
| ------- | ------------ | ------ | ---- | ---- | ---------------- |
| result  | 返回结果标识 | String | 10   | 是   | S0000            |
| message | 返回结果内容 | String | 不限 | 是   | BASE64(成功信息) |

#### **4.1.4.5** ***\*参数示例\****

l 请求参数

{

"billBatchCode": "电子票据代码",

"billNo": "电子票据号码",

"random": "电子票据校验码"

}

l 返回参数

成功

{

 "result": "S0000",

 "message":"成功"

}

失败

{

"result": "E0002",

 "message": "错误"

}

### **4.1.5** ***\*获取\*******\*电子票据\*******\*清单总页数\*******\*接口\****

业务系统根据电子票据信息，向医疗电子票据管理平台发起获取电子票据清单总页数请求，返回清单总页数，如电子票据不含清单，则返回值为平台错误提示信息。

#### **4.1.5.1** ***\*服务标识\**** ***\*getElectBill\*******\*lList\*******\*Total\****

#### **4.1.5.2** ***\*服务版本号\****

请求参数中version=1.0

#### **4.1.5.3** ***\*请求业务参数\****

| 参数          | 描述           | 类型   | 长度 | 必填 | 补充 |
| ------------- | -------------- | ------ | ---- | ---- | ---- |
| billBatchCode | 电子票据代码   | String | 50   | 是   |      |
| billNo        | 电子票据号     | String | 20   | 是   |      |
| random        | 电子票据校验码 | String | 20   | 是   |      |

#### **4.1.5.4** ***\*返回结果参数\****

| 参数    | 描述         | 类型   | 长度 | 必填 | 补充                                         |
| ------- | ------------ | ------ | ---- | ---- | -------------------------------------------- |
| result  | 返回结果标识 | String | 10   | 是   | S0000                                        |
| message | 返回结果内容 | String | 不限 | 是   | ***\*详见 B-1，JSON格式\*******\*BASE64\**** |

***\*B\*******\*-\*******\*1\**** ***\*返回\*******\*结果内容message\*******\*，响应\*******\*业务参数\****

| 参数  | 描述   | 类型    | 长度 | 必填 | 补充 |
| ----- | ------ | ------- | ---- | ---- | ---- |
| total | 总页数 | Integer | 10   | 是   |      |

#### **4.1.5.5** ***\*参数示例\****

l 请求参数

{

"billBatchCode": "电子票据代码",

"billNo": "电子票据号码",

"random": "电子票据校验码"

}

l 返回参数

成功

{

 "result": "S0000",

 "message"::{

"total": "总页数"

}

}

失败

{

"result": "E0002",

 "message": "错误"

}

 

### **4.1.6** ***\*打印电子票据\*******\*清单\*******\*接口\****

业务系统根据电子票据信息，向医疗电子票据管理平台发起打印电子票据清单请求，并完成电子票据清单打印（如：电子票据清单图片，打印在A4纸上）。

注：仅适用于业务系统调用博思客户端组件（通过JS访问组件或直接访问组件）。

#### **4.1.6.1** ***\*服务标识\**** ***\*printElectBillList\****

#### **4.1.6.2** ***\*服务版本号\****

请求参数中version=1.0

#### **4.1.6.3** ***\*请求业务参数\****

| 参数          | 描述           | 类型    | 长度 | 必填 | 补充                                                         |
| ------------- | -------------- | ------- | ---- | ---- | ------------------------------------------------------------ |
| billBatchCode | 电子票据代码   | String  | 50   | 是   |                                                              |
| billNo        | 电子票据号     | String  | 20   | 是   |                                                              |
| random        | 电子票据校验码 | String  | 20   | 是   |                                                              |
| total         | 总页数         | Integer | 10   | 是   | 调用接口【***\*获取电子票据清单总页数接口\****】获取返回值，如总页数值为0指不含清单 |
| pageNoBgn     | 起始页码       | Integer | 10   | 是   | <=终止页码<=总页数打印清单如需打印电子票据，起始页码设置为0  |
| pageNoEnd     | 终止页码       | Integer | 10   | 是   | >=起始页码<=总页数                                           |

***\*注：如需要打印所有页，则起始页码=1，终止页码=总页数；如需要指定范围，起始、终止页码按需填写；\****

**①**　***\*通过获取电子票据清单总页数接口，获取清单页数\****

**②**　***\*如存在清单页，起始页码=0时，会从电子票据图片开始打印\****

**③**　***\*如不存在清单页，可直接调用打印电子票据接口，也可用调用打印电子票据清单接口，传入参数总页数=0、起始页码=0、终止页码=0，即可打印电子票据图片\****

#### **4.1.6.4** ***\*返回结果参数\****

| 参数    | 描述         | 类型   | 长度 | 必填 | 补充             |
| ------- | ------------ | ------ | ---- | ---- | ---------------- |
| result  | 返回结果标识 | String | 10   | 是   | S0000            |
| message | 返回结果内容 | String | 不限 | 是   | BASE64(成功信息) |

#### **4.1.6.5** ***\*参数示例\****

l 请求参数

{

"billBatchCode": "电子票据代码",

"billNo": "电子票据号码",

"random": "电子票据校验码",

"total": "10",

"pageNoBgn":"1",

"pageNoEnd": "10"

 

}

l 返回参数

成功

{

 "result": "S0000",

 "message":"成功"

}

失败

{

"result": "E0002",

 "message": "错误"

}

## **4.2** ***\*财务核对\****

### **4.2.1** ***\*数据核对\****

#### **4.2.1.1** ***\*总笔数核对接口\****

业务系统根据业务日期、业务标识、开票点编码，向医疗电子票据管理平台发起总笔数核对请求，获取业务日期对应的电子票据信息。

##### 4.2.1.1.1 **服务标识 checkTotalData**

##### 4.2.1.1.2 **服务版本号**

请求参数中version=1.0

##### 4.2.1.1.3 **请求业务参数**

| 参数         | 描述         | 类型    | 长度 | 必填 | 补充                                                         |
| ------------ | ------------ | ------- | ---- | ---- | ------------------------------------------------------------ |
| busDate      | 业务日期     | String  | 10   | 是   | 格式:yyyy-MM-dd                                              |
| busType      | 业务标识     | String  | 20   | 否   | 直接填写业务系统内部编码值，由医疗平台配置对照列如：[附录5 业务标识列表](#_附录5：业务类型列表) |
| placeCode    | 开票点编码   | String  | 50   | 否   | 直接填写业务系统内部编码值，由医疗平台配置对照               |
| isReturnList | 是否返回列表 | String  | 1    | 否   | 0:不返回，1:返回空:返回                                      |
| pageNo       | 页码         | Integer | 10   | 是   | 传入值为1代表第一页,传入值为2代表第二页,依此类推.默认返回的数据是从第一页开始；第二次传入的页码计算由返回值中的总条数/每页条数 |
| pageSize     | 每页条数     | Integer | 10   | 是   | 每页返回最多返回200条                                        |

##### 4.2.1.1.4 **返回结果参数**

| 参数    | 描述         | 类型   | 长度 | 必填 | 补充                                         |
| ------- | ------------ | ------ | ---- | ---- | -------------------------------------------- |
| result  | 返回结果标识 | String | 10   | 是   | S0000                                        |
| message | 返回结果内容 | String | 不限 | 是   | ***\*详见 B-1，JSON格式\*******\*BASE64\**** |

***\*B\*******\*-\*******\*1\**** ***\*返回\*******\*结果内容message\*******\*，响应\*******\*业务参数\****

| 参数        | 描述           | 类型    | 长度 | 必填 | 补充                         |
| ----------- | -------------- | ------- | ---- | ---- | ---------------------------- |
| busDate     | 业务日期       | String  | 10   | 是   |                              |
| copyNum     | 总笔数         | Integer |      | 是   | **用于分页**                 |
| totalAmt    | 当前页总金额   | Number  | 18,2 | 是   |                              |
| totalNum    | 当前页总开票数 | Integer |      | 是   |                              |
| allTotalAmt | 所有页总金额   | Number  | 18,2 | 是   |                              |
| allTotalNum | 所有页总开票数 | Integer | 10   | 是   |                              |
| billNoList  | 票号段明细     | String  | 不限 | 是   | ***\*详见B-1-1,JSON格式\**** |

***\*B\*******\*-1-1 票号段明细列表billNoList\****

| 参数          | 描述             | 类型    | 长度 | 必填 | 补充 |
| ------------- | ---------------- | ------- | ---- | ---- | ---- |
| busType       | 业务标识         | String  | 20   | 是   |      |
| placeCode     | 开票点编码       | String  | 50   | 是   |      |
| billName      | 票据种类名称     | String  | 100  | 是   |      |
| billBatchCode | 票据代码         | String  | 50   | 是   |      |
| bgnNo         | 起始号码         | String  | 20   | 是   |      |
| endNo         | 终止号码         | String  | 20   | 是   |      |
| copyNum       | 票号段内总开票数 | Integer |      | 是   |      |
| totalAmt      | 票号段内总金额   | Number  | 18,2 | 是   |      |

 

##### 4.2.1.1.5 **参数示例**

l 请求参数

{

"busDate": "业务日期",

"busType": "业务标识",

"placeCode": "开票点",

"isReturnList": "是否返回列表",

"pageNo": "页数",

"pageSize": "每页条数"

}

l 返回参数

成功

{

 "result": "S0000",

 "message":{

​    "busDate": "业务日期",

"copyNum": "总笔数",

"totalAmt": "当前页总金额",

"totalNum": "当前页总开票数",

"allTotalAmt": "所有页总金额",

"allTotalNum": "所有页总开票数",

"billNoList": [

{

"busType": "业务标识",

"placeCode": "开票点编码",

"billName": "票据种类名称",

"billBatchCode": "票据代码",

"bgnNo": "起始号码",

"endNo": "终止号码",

"copyNum": "票号段内总开票数",

"totalAmt": "票号段内总金额"

},

{

"busType": "业务标识",

"placeCode": "开票点编码",

"billName": "票据种类名称",

"billBatchCode": "票据代码",

"bgnNo": "起始号码",

"endNo": "终止号码",

"copyNum": "票号段内总开票数",

"totalAmt": "票号段内总金额"

}

]

}

}

失败

{

"result": "E0002",

 "message": "错误"

}

#### **4.2.1.2** ***\*退费数据核对接口\****

业务系统根据业务日期、业务标识、开票点编码，向医疗电子票据管理平台发起退费数据核对请求，获取对应的电子票据退费信息。

##### 4.2.1.2.1 **服务标识 checkWriteOffData** 

##### 4.2.1.2.2 **服务版本号**

请求参数中version=1.0

##### 4.2.1.2.3 **请求业务参数**

| 参数         | 描述         | 类型    | 长度 | 必填 | 补充                                                         |
| ------------ | ------------ | ------- | ---- | ---- | ------------------------------------------------------------ |
| busDate      | 业务日期     | String  | 10   | 是   | 格式:yyyy-MM-dd                                              |
| placeCode    | 开票点编码   | String  | 50   | 是   | 直接填写业务系统内部编码值，由医疗平台配置对照               |
| busType      | 业务标识     | String  | 20   | 是   | 直接填写业务系统内部编码值，由医疗平台配置对照列如：[附录5 业务标识列表](#_附录5：业务类型列表) |
| isReturnList | 是否返回列表 | String  | 1    | 否   | 0:不返回，1:返回空:返回                                      |
| pageNo       | 页码         | Integer | 10   | 是   | 传入值为1代表第一页,传入值为2代表第二页,依此类推.默认返回的数据是从第一页开始；第二次传入的页码计算由返回值中的总条数/每页条数 |
| pageSize     | 每页条数     | Integer | 10   | 是   | 每页返回最多返回200条                                        |

##### 4.2.1.2.4 **返回结果参数**

| 参数    | 描述         | 类型   | 长度 | 必填 | 补充                                         |
| ------- | ------------ | ------ | ---- | ---- | -------------------------------------------- |
| result  | 返回结果标识 | String | 10   | 是   | S0000                                        |
| message | 返回结果内容 | String | 不限 | 是   | ***\*详见 B-1，JSON格式\*******\*BASE64\**** |

***\*B\*******\*-\*******\*1\**** ***\*返回\*******\*结果内容message\*******\*，响应\*******\*业务参数\****

| 参数        | 描述               | 类型    | 长度 | 必填 | 补充                         |
| ----------- | ------------------ | ------- | ---- | ---- | ---------------------------- |
| busDate     | 业务日期           | String  | 10   | 是   |                              |
| copyNum     | 总笔数             | Integer |      | 是   | **用于分页**                 |
| totalAmt    | 当前页退费总金额   | Number  | 18,2 | 是   |                              |
| totalNum    | 当前页退费总开票数 | Integer | 10   | 是   |                              |
| allTotalAmt | 所有页总金额       | Number  | 18,2 | 是   |                              |
| allTotalNum | 所有页总开票数     | Integer | 10   | 是   |                              |
| billNoList  | 退费票号段明细     | String  | 不限 | 是   | ***\*详见B-1-1,JSON格式\**** |

***\*B\*******\*-1-1 票号段明细列表billNoList\****

| 参数          | 描述               | 类型    | 长度 | 必填 | 补充 |
| ------------- | ------------------ | ------- | ---- | ---- | ---- |
| busType       | 业务标识           | String  | 20   | 是   |      |
| placeCode     | 开票点编码         | String  | 50   | 是   |      |
| billName      | 票据种类名称       | String  | 100  | 是   |      |
| billBatchCode | 票据代码           | String  | 50   | 是   |      |
| bgnNo         | 起始号码           | String  | 20   | 是   |      |
| endNo         | 终止号码           | String  | 20   | 是   |      |
| copyNum       | 票号段内退费总票数 | Integer |      | 是   |      |
| totalAmt      | 票号段内退费总金额 | Number  | 18,2 | 是   |      |

 

##### 4.2.1.2.5 **参数示例**

l 请求参数

{

"busDate": "业务日期",

"placeCode": "开票点编码",

"busType": "业务标识",

"isReturnList": "是否返回列表",

"pageNo": "页数",

"pageSize": "每页条数"

}

l 返回参数

成功

{

 "result": "S0000",

 "message":{

​    "busDate": "业务日期",

"copyNum": "退费总笔数",

"totalAmt": "当前页退费总金额",

"totalNum": "当前页退费总开票数",

"allTotalAmt": "所有页总金额",

"allTotalNum": "所有页总开票数",

"billNoList": [

{

"placeCode": "开票点编码",

"busType": "业务标识",

"billName": "票据种类名称",

"billBatchCode": "票据代码",

"bgnNo": "起始号码",

"endNo": "终止号码",

"copyNum": "票号段内退费总票数",

"totalAmt": "票号段内退费总金额"

},

{

"placeCode": "开票点编码",

"busType": "业务标识",

"billName": "票据种类名称",

"billBatchCode": "票据代码",

"bgnNo": "起始号码",

"endNo": "终止号码",

"copyNum": "票号段内退费总票数",

"totalAmt": "票号段内退费总金额"

}

]

}

}

失败

{

"result": "E0002",

 "message": "错误"

}

#### **4.2.1.3** ***\*根据业务时间获取开票信息接口\****

业务系统根据业务日期、开票点编码，向医疗电子票据管理平台发起根据业务时间获取开票信息请求，获取业务时间范围内的开票信息。

##### 4.2.1.3.1 **服务标识 getBillByBusDate**

##### 4.2.1.3.2 **服务版本号**

请求参数中version=1.0

##### 4.2.1.3.3 **请求业务参数**

| 参数       | 描述         | 类型    | 长度 | 必填 | 补充                                                         |
| ---------- | ------------ | ------- | ---- | ---- | ------------------------------------------------------------ |
| busBgnDate | 业务起始日期 | String  | 17   | 是   | 格式：yyyyMMddHHmmssSSS                                      |
| busEndDate | 业务截止日期 | String  | 17   | 是   | 格式：yyyyMMddHHmmssSSS                                      |
| placeCode  | 开票点编码   | String  | 50   | 否   | 直接填写业务系统内部编码值，由医疗平台配置对照，不填写，则查询单位下所有开票据点 |
| dataType   | 数据类型     | String  | 1    | 否   | 1 正常电子、2 电子红票、3 换开纸质、4 换开纸质红票、5空白纸质 |
| pageNo     | 页码         | Integer | 10   | 是   | 传入值为1代表第一页,传入值为2代表第二页,依此类推.默认返回的数据是从第一页开始；第二次传入的页码计算由返回值中的总条数/每页条数 |
| pageSize   | 每页条数     | Integer | 10   | 是   | 每页返回最多返回200条                                        |

##### 4.2.1.3.4 **返回结果参数**

| 参数    | 描述         | 类型   | 长度 | 必填 | 补充                                         |
| ------- | ------------ | ------ | ---- | ---- | -------------------------------------------- |
| result  | 返回结果标识 | String | 10   | 是   | S0000                                        |
| message | 返回结果内容 | String | 不限 | 是   | ***\*详见 B-1，JSON格式\*******\*BASE64\**** |

***\*B\*******\*-\*******\*1\**** ***\*返回\*******\*结果内容message\*******\*，响应\*******\*业务参数\****

| 参数     | 描述         | 类型    | 长度 | 必填 | 补充                             |
| -------- | ------------ | ------- | ---- | ---- | -------------------------------- |
| total    | 总条数       | Integer | 10   | 是   | **用于分页**                     |
| pageNo   | 当前页码     | Integer | 10   | 是   |                                  |
| billList | 开票明细列表 | String  | 不限 | 是   | ***\*详见B-1-1,JSON格式列表\**** |

***\*B\*******\*-1-1 开票明细列表billList\****

| 参数                | 描述             | 类型   | 长度 | 必填 | 补充                                                         |
| ------------------- | ---------------- | ------ | ---- | ---- | ------------------------------------------------------------ |
| busDate             | 业务日期         | String | 17   | 是   | 格式:yyyyMMddHHmmssSSS                                       |
| busNo               | 业务流水号       | String | 50   | 是   |                                                              |
| busType             | 业务标识         | String | 20   | 是   |                                                              |
| placeCode           | 开票点编码       | String | 50   | 是   |                                                              |
| billName            | 票据种类名称     | String | 100  | 是   |                                                              |
| billBatchCode       | 票据代码         | String | 50   | 是   |                                                              |
| billNo              | 票据号码         | String | 20   | 是   |                                                              |
| random              | 校验码           | String | 20   | 是   |                                                              |
| totalAmt            | 总金额           | Number | 14,2 | 是   |                                                              |
| ivcDateTime         | 开票时间         | String | 17   | 是   | yyyyMMddHHmmssSSS                                            |
| dataType            | 数据类型         | String | 1    | 是   | 1 正常电子、2 电子红票、3 换开纸质、4 换开纸质红票、5空白纸质 |
| state               | 状态             | String | 1    | 是   | 1正常 、2作废 、3冲红                                        |
| relateBillBatchCode | 关联电子票据代码 | String | 50   | 否   |                                                              |
| relateBillNo        | 关联电子票据号码 | String | 20   | 否   |                                                              |

 

##### 4.2.1.3.5 **参数示例**

l 请求参数

{

"busBgnDate": "业务起始日期",

"busEndDate": "业务截止日期",

"placeCode": "开票点编码",

"dataType": "数据类型",

"pageNo": "页数",

"pageSize": "每页条数"

}

l 返回参数

成功

{

 "result": "S0000",

 "message":{

​    "pageNo": "当前页码",

 "total": "总数",

"billList": [{

"busDate": "业务日期",

"busNo": "业务流水号",

"busType": "业务标识",

"placeCode": "开票点编码",

"billBatchCode": "票据代码",

"billNo": "票据号码",

"random": "校验码",

"totalAmt": "总金额",

"ivcDateTime": "开票时间",

"dataType": "数据类型",

"relationBillBatchCode": "关联电子票据代码",

"relationBillNo": "关联电子票据号码"

},

{

"busDate": "业务日期",

"busNo": "业务流水号",

"busType": "业务标识",

"placeCode": "开票点编码",

"billBatchCode": "票据代码",

"billNo": "票据号码",

"random": "校验码",

"totalAmt": "总金额",

"ivcDateTime": "开票时间",

"dataType": "数据类型",

"relationBillBatchCode": "关联电子票据代码",

"relationBillNo": "关联电子票据号码"

}]

}

}

失败

{

"result": "E0002",

 "message": "错误"

}

#### **4.2.1.4** ***\*根据开票日期获取\*******\*总笔数核对接口\****

业务系统根据开票日期、业务标识、开票点编码，向医疗电子票据管理平台发起总笔数核对请求，获取开票日期对应的电子票据信息。

##### 4.2.1.4.1 **服务标识 checkTotalData****ByIvcDate**

##### 4.2.1.4.2 **服务版本号**

请求参数中version=1.0

##### 4.2.1.4.3 **请求业务参数**

| 参数         | 描述         | 类型    | 长度 | 必填 | 补充                                                         |
| ------------ | ------------ | ------- | ---- | ---- | ------------------------------------------------------------ |
| ivcDate      | 开票日期     | String  | 10   | 是   | 格式:yyyy-MM-dd                                              |
| busType      | 业务标识     | String  | 20   | 否   | 直接填写业务系统内部编码值，由医疗平台配置对照列如：[附录5 业务标识列表](#_附录5：业务类型列表) |
| placeCode    | 开票点编码   | String  | 50   | 否   | 直接填写业务系统内部编码值，由医疗平台配置对照               |
| isReturnList | 是否返回列表 | String  | 1    | 否   | 0:不返回，1:返回空:返回                                      |
| pageNo       | 页码         | Integer | 10   | 是   | 传入值为1代表第一页,传入值为2代表第二页,依此类推.默认返回的数据是从第一页开始；第二次传入的页码计算由返回值中的总条数/每页条数 |
| pageSize     | 每页条数     | Integer | 10   | 是   | 每页返回最多返回200条                                        |

##### 4.2.1.4.4 **返回结果参数**

| 参数    | 描述         | 类型   | 长度 | 必填 | 补充                                         |
| ------- | ------------ | ------ | ---- | ---- | -------------------------------------------- |
| result  | 返回结果标识 | String | 10   | 是   | S0000                                        |
| message | 返回结果内容 | String | 不限 | 是   | ***\*详见 B-1，JSON格式\*******\*BASE64\**** |

***\*B\*******\*-\*******\*1\**** ***\*返回\*******\*结果内容message\*******\*，响应\*******\*业务参数\****

| 参数        | 描述           | 类型    | 长度 | 必填 | 补充               |
| ----------- | -------------- | ------- | ---- | ---- | ------------------ |
| ivcDate     | 开票日期       | String  | 10   | 是   | 格式:yyyy-MM-dd    |
| copyNum     | 总笔数         | Integer |      | 是   | 用于分页           |
| totalAmt    | 当前页总金额   | Number  | 18,2 | 是   |                    |
| totalNum    | 当前页总开票数 | Integer |      | 是   |                    |
| allTotalAmt | 所有页总金额   | Number  | 18,2 | 是   |                    |
| allTotalNum | 所有页总开票数 | Integer | 10   | 是   |                    |
| billNoList  | 票号段明细     | String  | 不限 | 是   | 详见B-1-1,JSON格式 |

***\*B\*******\*-1-1 票号段明细列表billNoList\****

| 参数          | 描述             | 类型    | 长度 | 必填 | 补充 |
| ------------- | ---------------- | ------- | ---- | ---- | ---- |
| busType       | 业务标识         | String  | 20   | 是   |      |
| placeCode     | 开票点编码       | String  | 50   | 是   |      |
| billName      | 票据种类名称     | String  | 100  | 是   |      |
| billBatchCode | 票据代码         | String  | 50   | 是   |      |
| bgnNo         | 起始号码         | String  | 20   | 是   |      |
| endNo         | 终止号码         | String  | 20   | 是   |      |
| copyNum       | 票号段内总开票数 | Integer |      | 是   |      |
| totalAmt      | 票号段内总金额   | Number  | 18,2 | 是   |      |

 

##### 4.2.1.4.5 **参数示例**

l 请求参数

{

"ivcDate": "开票日期",

"busType": "业务标识",

"placeCode": "开票点",

"isReturnList": "是否返回列表",

"pageNo": "页数",

"pageSize": "每页条数"

}

l 返回参数

成功

{

 "result": "S0000",

 "message":{

"ivcDate": "开票日期",     

"busDate": "业务日期",

"copyNum": "总笔数",

"totalAmt": "当前页总金额",

"totalNum": "当前页总开票数",

"allTotalAmt": "所有页总金额",

"allTotalNum": "所有页总开票数",

"billNoList": [

{

"busType": "业务标识",

"placeCode": "开票点编码",

"billName": "票据种类名称",

"billBatchCode": "票据代码",

"bgnNo": "起始号码",

"endNo": "终止号码",

"copyNum": "票号段内总开票数",

"totalAmt": "票号段内总金额"

},

{

"busType": "业务标识",

"placeCode": "开票点编码",

"billName": "票据种类名称",

"billBatchCode": "票据代码",

"bgnNo": "起始号码",

"endNo": "终止号码",

"copyNum": "票号段内总开票数",

"totalAmt": "票号段内总金额"

}

]

}

}

失败

{

"result": "E0002",

 "message": "错误"

}

#### **4.2.1.5** ***\*根据开票日期获取\*******\*退费数据核对接口\****

业务系统根据开票日期、业务标识、开票点编码，向医疗电子票据管理平台发起退费数据核对请求，获取对应的电子票据退费信息。

##### 4.2.1.5.1 **服务标识 checkWriteOffData****ByIvcDate**

##### 4.2.1.5.2 **服务版本号**

请求参数中version=1.0

##### 4.2.1.5.3 **请求业务参数**

| 参数         | 描述         | 类型    | 长度 | 必填 | 补充                                                         |
| ------------ | ------------ | ------- | ---- | ---- | ------------------------------------------------------------ |
| ivcDate      | 开票日期     | String  | 10   | 是   | 格式:yyyy-MM-dd                                              |
| placeCode    | 开票点编码   | String  | 50   | 是   | 直接填写业务系统内部编码值，由医疗平台配置对照               |
| busType      | 业务标识     | String  | 20   | 是   | 直接填写业务系统内部编码值，由医疗平台配置对照列如：[附录5 业务标识列表](#_附录5：业务类型列表) |
| isReturnList | 是否返回列表 | String  | 1    | 否   | 0:不返回，1:返回空:返回                                      |
| pageNo       | 页码         | Integer | 10   | 是   | 传入值为1代表第一页,传入值为2代表第二页,依此类推.默认返回的数据是从第一页开始；第二次传入的页码计算由返回值中的总条数/每页条数 |
| pageSize     | 每页条数     | Integer | 10   | 是   | 每页返回最多返回200条                                        |

##### 4.2.1.5.4 **返回结果参数**

| 参数    | 描述         | 类型   | 长度 | 必填 | 补充                                         |
| ------- | ------------ | ------ | ---- | ---- | -------------------------------------------- |
| result  | 返回结果标识 | String | 10   | 是   | S0000                                        |
| message | 返回结果内容 | String | 不限 | 是   | ***\*详见 B-1，JSON格式\*******\*BASE64\**** |

***\*B\*******\*-\*******\*1\**** ***\*返回\*******\*结果内容message\*******\*，响应\*******\*业务参数\****

| 参数        | 描述               | 类型    | 长度 | 必填 | 补充               |
| ----------- | ------------------ | ------- | ---- | ---- | ------------------ |
| ivcDate     | 开票日期           | String  | 10   | 是   | 格式:yyyy-MM-dd    |
| copyNum     | 总笔数             | Integer |      | 是   | 用于分页           |
| totalAmt    | 当前页退费总金额   | Number  | 18,2 | 是   |                    |
| totalNum    | 当前页退费总开票数 | Integer | 10   | 是   |                    |
| allTotalAmt | 所有页总金额       | Number  | 18,2 | 是   |                    |
| allTotalNum | 所有页总开票数     | Integer | 10   | 是   |                    |
| billNoList  | 退费票号段明细     | String  | 不限 | 是   | 详见B-1-1,JSON格式 |

***\*B\*******\*-1-1 票号段明细列表billNoList\****

| 参数          | 描述               | 类型    | 长度 | 必填 | 补充 |
| ------------- | ------------------ | ------- | ---- | ---- | ---- |
| busType       | 业务标识           | String  | 20   | 是   |      |
| placeCode     | 开票点编码         | String  | 50   | 是   |      |
| billName      | 票据种类名称       | String  | 100  | 是   |      |
| billBatchCode | 票据代码           | String  | 50   | 是   |      |
| bgnNo         | 起始号码           | String  | 20   | 是   |      |
| endNo         | 终止号码           | String  | 20   | 是   |      |
| copyNum       | 票号段内退费总票数 | Integer |      | 是   |      |
| totalAmt      | 票号段内退费总金额 | Number  | 18,2 | 是   |      |

 

##### 4.2.1.5.5 **参数示例**

l 请求参数

{

"ivcDate": "开票日期",

"placeCode": "开票点编码",

"busType": "业务标识",

"isReturnList": "是否返回列表",

"pageNo": "页数",

"pageSize": "每页条数"

}

l 返回参数

成功

{

 "result": "S0000",

 "message":{

"ivcDate": "开票日期",  

​    "busDate": "业务日期",

"copyNum": "退费总笔数",

"totalAmt": "当前页退费总金额",

"totalNum": "当前页退费总开票数",

"allTotalAmt": "所有页总金额",

"allTotalNum": "所有页总开票数",

"billNoList": [

{

"placeCode": "开票点编码",

"busType": "业务标识",

"billName": "票据种类名称",

"billBatchCode": "票据代码",

"bgnNo": "起始号码",

"endNo": "终止号码",

"copyNum": "票号段内退费总票数",

"totalAmt": "票号段内退费总金额"

},

{

"placeCode": "开票点编码",

"busType": "业务标识",

"billName": "票据种类名称",

"billBatchCode": "票据代码",

"bgnNo": "起始号码",

"endNo": "终止号码",

"copyNum": "票号段内退费总票数",

"totalAmt": "票号段内退费总金额"

}

]

}

}

失败

{

"result": "E0002",

 "message": "错误"

}

#### **4.2.1.6** ***\*根据\*******\*开票日期\*******\*获取开票信息接口\****

业务系统根据开票日期、开票点编码，向医疗电子票据管理平台发起根据开票时间获取开票信息请求，获取开票时间范围内的开票信息。

##### 4.2.1.6.1 **服务标识 getBill****ByIvcDate**

##### 4.2.1.6.2 **服务版本号**

请求参数中version=1.0

##### 4.2.1.6.3 **请求业务参数**

| 参数       | 描述       | 类型    | 长度 | 必填 | 补充                                                         |
| ---------- | ---------- | ------- | ---- | ---- | ------------------------------------------------------------ |
| busBgnDate | 起始日期   | String  | 17   | 是   | 格式：yyyyMMddHHmmssSSS                                      |
| busEndDate | 截止日期   | String  | 17   | 是   | 格式：yyyyMMddHHmmssSSS                                      |
| placeCode  | 开票点编码 | String  | 50   | 否   | 直接填写业务系统内部编码值，由医疗平台配置对照，不填写，则查询单位下所有开票据点 |
| dataType   | 数据类型   | String  | 1    | 否   | 1 正常电子、2 电子红票、3 换开纸质、4 换开纸质红票、5空白纸质 |
| busType    | 业务标识   | String  | 10   | 否   | 01住院、02门诊、03急诊、05门特、04体检中心                   |
| pageNo     | 页码       | Integer | 10   | 是   | 传入值为1代表第一页,传入值为2代表第二页,依此类推.默认返回的数据是从第一页开始；第二次传入的页码计算由返回值中的总条数/每页条数 |
| pageSize   | 每页条数   | Integer | 10   | 是   | 每页返回最多返回200条                                        |

##### 4.2.1.6.4 **返回结果参数**

| 参数    | 描述         | 类型   | 长度 | 必填 | 补充                                         |
| ------- | ------------ | ------ | ---- | ---- | -------------------------------------------- |
| result  | 返回结果标识 | String | 10   | 是   | S0000                                        |
| message | 返回结果内容 | String | 不限 | 是   | ***\*详见 B-1，JSON格式\*******\*BASE64\**** |

***\*B\*******\*-\*******\*1\**** ***\*返回\*******\*结果内容message\*******\*，响应\*******\*业务参数\****

| 参数     | 描述         | 类型    | 长度 | 必填 | 补充                             |
| -------- | ------------ | ------- | ---- | ---- | -------------------------------- |
| total    | 总条数       | Integer | 10   | 是   | **用于分页**                     |
| pageNo   | 当前页码     | Integer | 10   | 是   |                                  |
| billList | 开票明细列表 | String  | 不限 | 是   | ***\*详见B-1-1,JSON格式列表\**** |

***\*B\*******\*-1-1 开票明细列表billList\****

| 参数                | 描述             | 类型   | 长度 | 必填 | 补充                                                         |
| ------------------- | ---------------- | ------ | ---- | ---- | ------------------------------------------------------------ |
| busDate             | 业务日期         | String | 17   | 是   | 格式:yyyyMMddHHmmssSSS                                       |
| busNo               | 业务流水号       | String | 50   | 是   |                                                              |
| busType             | 业务标识         | String | 20   | 是   |                                                              |
| placeCode           | 开票点编码       | String | 50   | 是   |                                                              |
| billName            | 票据种类名称     | String | 100  | 是   |                                                              |
| billBatchCode       | 票据代码         | String | 50   | 是   |                                                              |
| billNo              | 票据号码         | String | 20   | 是   |                                                              |
| random              | 校验码           | String | 20   | 是   |                                                              |
| totalAmt            | 总金额           | Number | 18,2 | 是   |                                                              |
| ivcDateTime         | 开票时间         | String | 17   | 是   | yyyyMMddHHmmssSSS                                            |
| dataType            | 数据类型         | String | 1    | 是   | 1 正常电子、2 电子红票、3 换开纸质、4 换开纸质红票、5空白纸质 |
| state               | 状态             | String | 1    | 是   | 1正常 、2作废 、3冲红                                        |
| relateBillBatchCode | 关联电子票据代码 | String | 50   | 否   |                                                              |
| relateBillNo        | 关联电子票据号码 | String | 20   | 否   |                                                              |

 

##### 4.2.1.6.5 **参数示例**

l 请求参数

{

"busBgnDate": "起始日期",

"busEndDate": "截止日期",

"placeCode": "开票点编码",

"dataType": "数据类型",

"pageNo": "页数",

"pageSize": "每页条数"

}

l 返回参数

成功

{

 "result": "S0000",

 "message":{

​    "pageNo": "当前页码",

 "total": "总数",

"billList": [{

"busDate": "业务日期",

"busNo": "业务流水号",

"busType": "业务标识",

"placeCode": "开票点编码",

"billBatchCode": "票据代码",

"billNo": "票据号码",

"random": "校验码",

"totalAmt": "总金额",

"ivcDateTime": "开票时间",

"dataType": "数据类型",

"relationBillBatchCode": "关联电子票据代码",

"relationBillNo": "关联电子票据号码"

},

{

"busDate": "业务日期",

"busNo": "业务流水号",

"busType": "业务标识",

"placeCode": "开票点编码",

"billBatchCode": "票据代码",

"billNo": "票据号码",

"random": "校验码",

"totalAmt": "总金额",

"ivcDateTime": "开票时间",

"dataType": "数据类型",

"relationBillBatchCode": "关联电子票据代码",

"relationBillNo": "关联电子票据号码"

}]

}

}

失败

{

"result": "E0002",

 "message": "错误"

}

 

# ***\*附录1：JSON说明\****

JSON(JavaScript Object Notation) 是一种轻量级的数据交换格式。易于人阅读和编

写，同时也易于机器解析和生成。它基于JavaScript（Standard ECMA-262 3rd Edition -

December 1999）的一个子集。JSON 采用完全独立于语言的文本格式，但是也使用了类似

于C 语言家族的习惯（包括C, C++, C#, Java, JavaScript, Perl, Python 等）。这些特性

使JSON 成为理想的数据交换语言。

（1）JSON 构建的结构：

\1. “名称/值”对的集合（A collection of name/value pairs）。不同的语言中，它

被理解为对象（object），记录（record），结构（struct），字典（dictionary），哈希表（h

ash table），有键列表（keyed list），或者关联数组（associative array）。

\2. 值的有序列表（An ordered list of values）。在大部分语言中，它被理解为数组

（array）。

这些都是常见的数据结构。事实上大部分现代计算机语言都以某种形式支持它们。这使

得一种数据格式在同样基于这些结构的编程语言之间交换成为可能。

（2）JSON 的具体形式

1、对象是一个无序的“‘名称/值’对”集合。一个对象以“{”（左括号）开始，“}”

（右括号）结束。每个“名称”后跟一个“:”（冒号）；“‘名称/值’对”之间使用“,”

（逗号）分隔。

2、数组是值（value）的有序集合。一个数组以“[”（左中括号）开始，“]”（右中

括号）结束。值之间使用“,”（逗号）分隔。

3、值（value）可以是双引号括起来的字符串（string）、数值(number)、true、fals

e、null、对象（object）或者数组（array）。这些结构可以嵌套。

4、字符串（string）是由双引号包围的任意数量Unicode 字符的集合，使用反斜线转

义。一个字符（character）即一个单独的字符串（character string）。字符串（string）

与C 或者Java 的字符串非常相似。

5、数值（number）也与C 或者Java 的数值非常相似。除去未曾使用的八进制与十六进制格式。除去一些编码细节。

（3）JSON 实例：

用JSON 表示中国部分省市数据如下：

{

"name": "中国",

"province": [

​    {

"name": "黑龙江",

"citys": {

"city": [

"哈尔滨",

"大庆"

​        ]

​      }

​    },

​    {

"name": "广东",

"citys": {

"city": [

"深圳",

"珠海"

​        ]

​      }

​    },

​    {

"name": "台湾",

"citys": {

"city": [

"台北",

"高雄"

​        ]

​      }

​    },

​    {

"name": "新疆",

"citys": {

"city": [

"乌鲁木齐"

​        ]

​      }

​    }

  ]

}通过json的索引.province[0].name 就能够读取“黑龙江”这个值

# ***\*附录2：数据格式\****

数字：分为Integer与Number，Integer为不定长度的整型数，Number是带小数或固定长度的数字。

# ***\*附录3：卡类型列表\****

| 编码 | 名称          |
| ---- | ------------- |
| 1101 | 身份证号码    |
| 1102 | 社会保障卡号  |
| 3101 | 诊疗卡/就诊卡 |
| 4101 | 居民户口簿    |
| 5101 | 京通卡        |

 

# ***\*附录4：\*******\*交费渠道\*******\*列表\****

| 编码 | 名称       |
| ---- | ---------- |
| 01   | POS刷卡    |
| 02   | 现金       |
| 03   | 转账       |
| 04   | 支付宝     |
| 05   | 微信       |
| 06   | 支票       |
| 07   | 卡支付     |
| 08   | 银联卡     |
| 09   | 自助机缴费 |
| 10   | 软POS      |
| 11   | 医保支付   |
| 12   | 预交金     |
| 13   | 公众平台   |

# ***\*附录5：业务标识\*******\*列表\****

业务标识对应具体API服务标识

| 编码 | 名称       |
| ---- | ---------- |
| 01   | 住院       |
| 02   | 门诊       |
| 03   | 急诊       |
| 04   | 门特       |
| 05   | 体检中心   |
| 06   | 挂号       |
| 07   | 住院预交金 |
| 08   | 体检预交金 |
| 09   | 往来票     |
| 10   | 捐赠票     |
| 11   | 非税通用票 |
| 12   | 门诊预交金 |

# ***\*附录6：通知类型\****

| 编码 | 名称     |
| ---- | -------- |
| 1201 | 移动电话 |
| 1202 | 电子邮件 |

# ***\*附录7：\*******\*医保报销类型列表\****

| 编码 | 名称                 |
| ---- | -------------------- |
| 01   | 公务员报销           |
| 02   | 基本医疗报销         |
| 03   | 补充报销             |
| 04   | 其它（可以按需添加） |

# ***\*附录8：编码对照说明\****

 第三方业务系统中的单位编码、项目编码、票据种类编码、票据代码、开票点编码与医疗电子票据管理平台不一致时，需要先在医疗电子票据管理平台中配置对照关系。

# ***\*附录9：返回结果标识列表\****

| 编码  | 描述                                                 |
| ----- | ---------------------------------------------------- |
| S0000 | 成功的返回代码                                       |
| EXXXX | 除了S0000之外的代码,其它所有值都表示错误的返回代码。 |

# ***\*附录\*******\*10\*******\*：\*******\*his对接的注意事项\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml17576\wps2.png)