# 企业端接口系统部署文档

## 概述

本文档描述了企业端接口系统的部署和使用方法。该系统提供RESTful API接口，支持企业端系统对接体检预约、分组管理和人员名单导入功能。

## 系统架构

### 技术栈
- **后端框架**: Spring Boot + MyBatis Plus
- **鉴权方式**: API Key + API Secret + 签名验证
- **数据库**: MySQL
- **接口风格**: RESTful API
- **响应格式**: JSON

### 核心功能模块
1. **API鉴权系统**: 基于API Key/Secret的安全认证
2. **企业预约管理**: 创建和管理企业体检预约
3. **分组管理**: 企业内部分组配置
4. **人员名单导入**: 批量导入体检人员信息

## 数据库表结构

### 1. API客户端配置表 (api_client)

```sql
CREATE TABLE `api_client` (
  `id` varchar(36) NOT NULL COMMENT '主键ID',
  `client_name` varchar(100) NOT NULL COMMENT '客户端名称',
  `api_key` varchar(64) NOT NULL COMMENT 'API密钥',
  `api_secret` varchar(128) NOT NULL COMMENT 'API秘钥',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：1-启用，0-禁用',
  `expire_time` datetime DEFAULT NULL COMMENT '过期时间',
  `ip_whitelist` text COMMENT 'IP白名单，多个IP用逗号分隔',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` tinyint(1) DEFAULT '0' COMMENT '删除标志：0-正常，1-删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_api_key` (`api_key`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='API客户端配置表';
```

## API接口文档

### 鉴权机制

所有API请求都需要在HTTP头中包含以下鉴权信息：

```
X-API-Key: {your_api_key}
X-Timestamp: {current_timestamp_in_milliseconds}
X-Nonce: {random_string}
X-Signature: {calculated_signature}
```

#### 签名算法
```
signature = MD5(apiKey + apiSecret + timestamp + nonce).toUpperCase()
```

#### 示例代码 (Java)
```java
public class ApiSignUtil {
    public static String generateSignature(String apiKey, String apiSecret, String timestamp, String nonce) {
        String signString = apiKey + apiSecret + timestamp + nonce;
        return DigestUtils.md5Hex(signString).toUpperCase();
    }
}
```

### 接口列表

#### 1. 健康检查
```
GET /api/v1/company/health
```

**响应示例:**
```json
{
  "code": 200,
  "message": "Success",
  "data": "OK",
  "timestamp": 1690704000000
}
```

#### 2. 创建企业预约
```
POST /api/v1/company/registration
```

**请求体:**
```json
{
  "companyId": "company123",
  "companyName": "测试企业",
  "regName": "2024年度体检",
  "examType": "exam_type_001",
  "startCheckDate": "2024-08-01",
  "endCheckDate": "2024-08-31",
  "personCount": 100,
  "serviceManager": "manager001",
  "teams": [
    {
      "teamNum": "T001",
      "name": "管理层",
      "examCategory": "category_001",
      "post": "管理岗",
      "sexLimit": "不限",
      "minAge": 25,
      "maxAge": 60,
      "price": 500.00,
      "discount": 0.9
    }
  ]
}
```

**响应示例:**
```json
{
  "code": 200,
  "message": "Company registration created successfully",
  "data": {
    "id": "reg123456",
    "companyId": "company123",
    "companyName": "测试企业",
    "regName": "2024年度体检",
    "companyReportNo": "CR20240730001",
    "createTime": "2024-07-30 10:00:00"
  },
  "timestamp": 1690704000000
}
```

#### 3. 批量创建客户登记
```
POST /api/v1/company/customers/batch
```

**请求体:**
```json
{
  "companyRegId": "reg123456",
  "customerList": [
    {
      "name": "张三",
      "gender": "男",
      "idCard": "110101199001011234",
      "phone": "13800138000",
      "teamName": "管理层",
      "appointmentDate": "2024-08-15"
    },
    {
      "name": "李四",
      "gender": "女",
      "idCard": "110101199002021234",
      "phone": "13800138001",
      "teamName": "管理层",
      "appointmentDate": "2024-08-16"
    }
  ]
}
```

**响应示例:**
```json
{
  "code": 200,
  "message": "All customer registrations created successfully",
  "data": {
    "total": 2,
    "successCount": 2,
    "failureCount": 0,
    "successList": [
      {
        "id": "cust001",
        "name": "张三",
        "examNo": "E20240730001",
        "status": "未登记"
      },
      {
        "id": "cust002",
        "name": "李四",
        "examNo": "E20240730002",
        "status": "未登记"
      }
    ],
    "failureList": []
  },
  "timestamp": 1690704000000
}
```

#### 4. 获取企业预约信息
```
GET /api/v1/company/registration/{companyRegId}
```

#### 5. 获取客户登记列表
```
GET /api/v1/company/registration/{companyRegId}/customers?pageNo=1&pageSize=20
```

## 部署步骤

### 1. 数据库初始化
执行上述SQL脚本创建`api_client`表。

### 2. 创建API客户端
通过系统管理界面创建API客户端配置：
1. 访问 `/comInterface/apiClient/list`
2. 点击"新增"按钮
3. 填写客户端名称、过期时间、IP白名单等信息
4. 系统自动生成API Key和API Secret

### 3. 配置验证
使用健康检查接口验证API是否正常工作：
```bash
curl -X GET "http://your-domain/api/v1/company/health" \
  -H "X-API-Key: your_api_key" \
  -H "X-Timestamp: 1690704000000" \
  -H "X-Nonce: random123" \
  -H "X-Signature: calculated_signature"
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权（签名验证失败） |
| 403 | 禁止访问（客户端被禁用或IP不在白名单） |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 安全注意事项

1. **API Secret保密**: API Secret绝对不能暴露在客户端代码中
2. **时间戳验证**: 请求时间戳与服务器时间差不能超过5分钟
3. **IP白名单**: 建议配置IP白名单限制访问来源
4. **HTTPS**: 生产环境必须使用HTTPS协议
5. **日志监控**: 监控API调用日志，及时发现异常访问

## 常见问题

### Q: 签名验证失败怎么办？
A: 检查以下几点：
1. API Key和API Secret是否正确
2. 时间戳格式是否正确（毫秒级）
3. 签名算法是否按照文档实现
4. 请求头是否正确设置

### Q: 如何处理批量导入中的部分失败？
A: 系统会返回详细的成功和失败列表，客户端可以根据失败原因重新处理失败的记录。

### Q: API调用频率有限制吗？
A: 目前没有硬性限制，但建议合理控制调用频率，避免对系统造成压力。

## 测试步骤

### 1. 创建API客户端
```sql
INSERT INTO api_client (id, client_name, api_key, api_secret, status, create_time, del_flag)
VALUES ('test001', '测试客户端', 'test_api_key_123456789012345678901234', 'test_api_secret_1234567890123456789012345678901234567890123456789012345678901234', 1, NOW(), 0);
```

### 2. 测试健康检查
```bash
# 计算签名
timestamp=$(date +%s%3N)
nonce="test123"
api_key="test_api_key_123456789012345678901234"
api_secret="test_api_secret_1234567890123456789012345678901234567890123456789012345678901234"
signature=$(echo -n "${api_key}${api_secret}${timestamp}${nonce}" | md5sum | awk '{print toupper($1)}')

# 发送请求
curl -X GET "http://localhost:8080/api/v1/company/health" \
  -H "X-API-Key: ${api_key}" \
  -H "X-Timestamp: ${timestamp}" \
  -H "X-Nonce: ${nonce}" \
  -H "X-Signature: ${signature}"
```

### 3. 测试创建企业预约
```bash
# 创建请求体
cat > company_reg.json << EOF
{
  "companyId": "comp001",
  "companyName": "测试企业有限公司",
  "regName": "2024年度员工体检",
  "examType": "1",
  "startCheckDate": "2024-08-01",
  "endCheckDate": "2024-08-31",
  "personCount": 50,
  "serviceManager": "admin",
  "teams": [
    {
      "teamNum": "T001",
      "name": "管理层",
      "examCategory": "1",
      "post": "管理岗",
      "sexLimit": "不限",
      "minAge": 25,
      "maxAge": 60
    }
  ]
}
EOF

# 发送请求
curl -X POST "http://localhost:8080/api/v1/company/registration" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: ${api_key}" \
  -H "X-Timestamp: ${timestamp}" \
  -H "X-Nonce: ${nonce}" \
  -H "X-Signature: ${signature}" \
  -d @company_reg.json
```

### 4. 测试批量创建客户
```bash
# 创建请求体（使用上一步返回的companyRegId）
cat > customers.json << EOF
{
  "companyRegId": "返回的企业预约ID",
  "customerList": [
    {
      "name": "张三",
      "gender": "男",
      "idCard": "110101199001011234",
      "phone": "13800138000",
      "teamName": "管理层",
      "appointmentDate": "2024-08-15"
    },
    {
      "name": "李四",
      "gender": "女",
      "idCard": "110101199002021234",
      "phone": "13800138001",
      "teamName": "管理层",
      "appointmentDate": "2024-08-16"
    }
  ]
}
EOF

# 发送请求
curl -X POST "http://localhost:8080/api/v1/company/customers/batch" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: ${api_key}" \
  -H "X-Timestamp: ${timestamp}" \
  -H "X-Nonce: ${nonce}" \
  -H "X-Signature: ${signature}" \
  -d @customers.json
```

## 性能优化建议

### 1. 数据库优化
- 为`api_client`表的`api_key`字段创建唯一索引
- 为`customer_reg`表的`company_reg_id`字段创建索引
- 定期清理过期的API客户端记录

### 2. 缓存策略
- 对API客户端配置进行缓存，减少数据库查询
- 对企业预约信息进行缓存，提高查询性能

### 3. 限流策略
- 实现基于API Key的限流机制
- 设置合理的并发请求限制

## 监控和日志

### 1. 关键指标监控
- API调用次数和成功率
- 响应时间分布
- 错误率统计
- 客户端活跃度

### 2. 日志记录
- 记录所有API调用日志
- 记录鉴权失败日志
- 记录业务异常日志

### 3. 告警设置
- API调用失败率超过阈值时告警
- 响应时间超过阈值时告警
- 异常客户端访问时告警

## 版本升级

### V1.1 计划功能
- 支持OAuth2.0认证
- 增加API调用统计功能
- 支持Webhook回调
- 增加数据同步状态查询

### V1.2 计划功能
- 支持GraphQL接口
- 增加实时数据推送
- 支持多租户隔离
- 增加API版本管理

## 联系支持

如有技术问题，请联系技术支持团队：
- 邮箱：<EMAIL>
- 电话：400-xxx-xxxx
- 技术文档：http://docs.example.com
