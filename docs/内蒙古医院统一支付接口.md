\# 统一下单接口 ## OpenAPI Specification ```yaml openapi: 3.0.1 info:  title: ''  description: ''  version: 1.0.0 paths:  /realpay/gateway:    post:      summary: 统一下单接口      deprecated: false      description: 非窗口、自助机等终端。如微信公众号、微信小程序、支付宝生活号等终端可用。      tags:        - 支付接口      parameters: []      requestBody:        content:          application/json:            schema:              type: object              properties:                charset:                  type: string                  description: UTF-8                  title: 编码                method:                  type: string                  title: 方法名                  description: ' rop.trade.union.create'                reqDate:                  type: string                  title: 日期                  description: '20230404'                appId:                  type: string                  title: appid                  description: appId (平台统一下发)                bizContent:                  type: object                  properties:                    orderNo:                      type: string                      title: 订单号                      description: 订单号                    orderType:                      type: string                      title: 订单类型                      description: 订单类型1 住院 2门诊 4处方                    payWayType:                      type: string                      title: 支付方式                      description: 32 微信公众号。35微信小程序。。（具体支付平台提供）                    amount:                      type: string                      title: 金额                      description: 订单金额 。0.01                    notifyUrl:                      type: string                      title: 回调地址                      description: 支付成功后支付平台回调第三方接口 例：http://127.0.0.1/payNotify                    attach:                      type: string                      title: 扩展参数                      description: >-                        调用方附加参数，回调通知时原样回传 例如 “12235” 回调获取requet. getParameter                        (“attach”) 返回“12345” 例如 {“name”：“用户名”} 回调获取requet.                        getParameter (“attach”) 返回“{“name”：“用户名”}”                    spbillCreateIp:                      type: string                      title: 使用方ip                      description: 必须传正确的用户端IP 微信H5必传                    hisBiz:                      type: object                      properties:                        clinicCode:                          type: string                          title: 挂号流水号                        recipeNos:                          type: string                          title: 处方号，多个处方时，使用逗号分隔                        inpatientNo:                          type: string                          title: 住院号                        inpatientSeriNo:                          type: string                          title: 住院流水号                        markType:                          type: string                          title: 卡类型                        markNo:                          type: string                          title: 就诊卡号                        patientName:                          type: string                          title: 患者姓名                        IDCard:                          type: string                          title: 身份证                        patientPhone:                          type: string                          title: 手机号                      title: his业务集合                      description: '无特殊要求可不添加，字符串类型，JSON数据格式 例子：{‘clinicCode’:  ‘22211323’}'                      x-apifox-orders:                        - clinicCode                        - recipeNos                        - inpatientNo                        - inpatientSeriNo                        - markType                        - markNo                        - patientName                        - IDCard                        - patientPhone                      nullable: true                    insuranceParam:                      type: object                      properties: {}                      x-apifox-orders: []                      title: 医保集合                      description: 字符串类型，JSON数据格式，如有需要支付平台提供                  x-apifox-orders:                    - orderNo                    - orderType                    - payWayType                    - amount                    - notifyUrl                    - attach                    - spbillCreateIp                    - hisBiz                    - insuranceParam                  required:                    - orderNo                    - orderType                    - payWayType                    - amount                    - notifyUrl                    - hisBiz                    - insuranceParam                  title: 业务集合                  description: 请求参数的集合，json字符串形式                sign:                  type: string                  title: 签名                signType:                  type: string                  title: 签名类型                extras:                  type: string                  description: >-                    注：有些支付渠道需要特殊参数 各支付渠道特定的扩展参数。JSON数据格式，例：{ 'xxx':'aaa',                    'yyy':'bbb'} 支付宝前台回调：{'frontUrl': 'http:…… '}                    32:微信公众号支付：{'openid':'aaa' }                  title: 扩展参数                version:                  type: string                  title: 版本号                  description: 例：1.0              required:                - charset                - method                - reqDate                - appId                - bizContent                - sign                - signType                - extras                - version              x-apifox-orders:                - appId                - method                - reqDate                - charset                - bizContent                - sign                - signType                - extras                - version            example:              charset: UTF-8              method: rop.trade.union.create              reqDate: '20220921134407'              appId: 测试              bizContent: >-                {"orderNo":"cs10000000001","orderType":1,"payWayType":32,"amount":0.01,"notifyUrl":"http://127.0.0.1:9090/rawTransNotify","currencyType":156,"attach":"123","operatorName":null,"wapUrl":null,"spbillCreateIp":"***********","overTime":null,"timeoutExpress":null,"hisBiz":null,"clientType":null,"operatorCode":null}              sign: >-                Or5t1vu1PmNVuekvg4I23TPRF7IGVgu/ZHMD6mybo8KmGDoTTng4e0hG0x5W4AeMHRz4eUVfy1w8oSPq90N57LXBpxU0Lqu5jtvpQaHEFQo5uJ8iHCw1p8li1W1SIkqlRvlfvbDic5/rcDY8mcPx7zKO3mlg7QSTep7pTVgZV6I=              signType: RSA              extras: >-                {"trmId":null,"trmBchNo":null,"trmPosNo":null,"macKey":null,"subPayType":null,"clinicCode":null,"recipeNos":null,"inpatientSeriNo":null,"markNo":null,"markType":null,"passWord":null,"openid":"oTasLj92pwCrXsVb9LHYR-VkdskQ","phone":null,"userId":null,"authCode":null,"subject":null,"tradeId":null,"cyberSourceCardType":null,"cyberSourceCardNumber":null,"cyberSourceCardExpiryDate":null,"frontUrl":null,"cancelUrl":null,"locale":null,"payChannel":null,"productCode":null,"outTradeNo":null,"scene":null,"industry":null,"payAuthNo":null,"sellerId":null,"totalAmount":null,"insurancePayScene":null,"body":null,"timeoutExpress":null,"orgNo":null,"orgName":null,"serialNo":null,"billNo":null,"requestContent":null,"gmtOutCreate":null,"patientName":null,"isInsurance":null,"medicalCardInstId":null,"medicalCardId":null,"medicalRequesCotennt":null,"patientId":null,"patienName":null,"hisOperNum":null,"refundAmount":null,"hisRefundTime":null,"orgPowerTranId":null,"referUrl":null}              version: '1.0'      responses:        '200':          description: ''          content:            application/json:              schema:                type: object                properties:                  msgCode:                    type: string                    title: 接口code                  msg:                    type: string                    title: 接口描述                  charset:                    type: string                    title: 编码                  signType:                    type: string                    title: 编码格式                  sign:                    type: string                    title: 签名                  data:                    type: object                    properties:                      payInfo:                        type: string                        title: 业务参数                        description: 支付不同返回业务参数不同，具体支付平台提供                      insurancePayResult:                        type: string                        title: 医保参数                        description: 无特殊情况可不用                      payAppid:                        type: string                        title: 支付appid                        description: 无特殊情况可不用                    required:                      - payInfo                    x-apifox-orders:                      - payInfo                      - insurancePayResult                      - payAppid                    title: 业务集合                required:                  - msgCode                  - msg                  - charset                  - signType                  - sign                  - data                x-apifox-orders:                  - msgCode                  - msg                  - charset                  - signType                  - sign                  - data          headers: {}          x-apifox-name: 成功      security: []      x-apifox-folder: 支付接口      x-apifox-status: developing      x-run-in-apifox: https://app.apifox.com/web/project/2819878/apis/api-86580350-run components:  schemas: {}  securitySchemes: {} servers:  - url: http://test-cn.your-api-server.com    description: 测试环境 security: [] ```



\# 窗口条码付支付接口 ## OpenAPI Specification ```yaml openapi: 3.0.1 info:  title: ''  description: ''  version: 1.0.0 paths:  /realpay/gateway:    post:      summary: 窗口条码付支付接口      deprecated: false      description: 条码支付接口      tags:        - 支付接口      parameters: []      requestBody:        content:          application/json:            schema:              type: object              properties:                charset:                  type: string                  title: 编码格式                  description: UTF-8                method:                  type: string                  title: 方法名称                  description: rop.trade.pay                reqDate:                  type: string                  description: 例:20230406163237                  title: 时间戳                appId:                  type: string                  description: appId (平台统一下发)                  title: appid                bizContent:                  type: object                  properties:                    orderNo:                      type: string                      title: 订单号                      description: 一笔订单保证唯一，规则可自定义                      maxLength: 32                    payWayType:                      type: integer                      title: 支付方式                      description: 微信 33 支付宝 22 其余支付平台提供                    orderType:                      type: integer                      title: 业务类型                      description: 1:住院预交金 、2:门诊挂号 、3:专科门诊、4:处方  特殊业务支付平台提供                    authCode:                      type: string                      title: 付款码                      description: 支付授权码                    subject:                      type: string                      title: 描述                      description: 例：窗口扫码                    totalAmount:                      type: number                      examples:                        - 1.23                      title: 金额                    operatorName:                      type: string                      title: 操作员                      description: 操作员                      examples:                        - 小明                      nullable: true                    operatorCode:                      type: string                      title: '操作员编码 '                      description: '操作员编码 '                      examples:                        - '00001'                      nullable: true                    terminalId:                      type: string                      title: 终端号                      description: 除特殊业务需要，可不传                      nullable: true                    hisBiz:                      type: object                      properties:                        clinicCode:                          type: string                          title: 挂号流水号                          description: 挂号流水号                        recipeNos:                          type: string                          title: 处方号                          description: 多个处方时，使用逗号分隔                        inpatientNo:                          type: string                          title: 住院号                          description: "\t住院号"                        inpatientSeriNo:                          type: string                          title: 住院流水号                          description: 住院流水号                        markType:                          type: string                          title: 卡类型                          description: 卡类型                        patientName:                          type: string                          title: 患者名称                          description: 患者名称                        cardNo:                          type: string                          title: 病历号                          description: 病历号                        iDCard:                          type: string                          title: 身份证号                          description: 身份证号                        patientPhone:                          type: string                          title: 患者手机号                          description: 患者手机号                        deptName:                          type: string                          title: 科室                          description: 科室                        doctorName:                          type: string                          title: 业务医生                          description: 医生姓名                      x-apifox-orders:                        - clinicCode                        - recipeNos                        - inpatientNo                        - inpatientSeriNo                        - markType                        - patientName                        - iDCard                        - cardNo                        - patientPhone                        - deptName                        - doctorName                      title: 患者信息                      description: 请求参数的集合，json字符串形式(无需求可不传)                      required:                        - doctorName                  title: 业务参数                  description: 请求参数的集合，json字符串形式                  x-apifox-orders:                    - orderNo                    - payWayType                    - orderType                    - authCode                    - subject                    - totalAmount                    - operatorName                    - operatorCode                    - terminalId                    - hisBiz                  required:                    - orderNo                    - payWayType                    - orderType                    - authCode                    - subject                    - totalAmount                sign:                  type: string                  title: 签名                  description: 签名                signType:                  type: string                  description: RSA2                version:                  type: string                  description: '1.0'              required:                - charset                - method                - reqDate                - appId                - bizContent                - version                - sign                - signType              x-apifox-orders:                - appId                - method                - reqDate                - bizContent                - sign                - signType                - charset                - version              x-apifox-refs: {}            example:              charset: UTF-8              method: rop.trade.pay              reqDate: '20230406163237'              appId: 测试              bizContent: >-                {"orderNo":"123456712343567eeee33111","payWayType":83,"orderType":2,"authCode":"130499372231065418","subject":"ccc","totalAmount":0.01,"operatorName":"123","operatorCode":"123","terminalId":"1208kb48","hisBiz":{"clinicCode":"111","recipeNos":null,"inpatientNo":null,"inpatientSeriNo":null,"markType":null,"markNo":null,"patientName":null,"idCard":null,"cardNo":"1111","patientPhone":null}}              sign: >-                wJnDF+KOxVNYZMvKkN36aS4yUtjXVA/wT8ij4NIiLFYhFBRTlyk4TYbRq943Fv7fBJXXtDc3nBhOaEyMjp1l3y8RTflwrqAEveX6cSBJcxcqlsUj61/vM2h2TRdA1+kocqc1qaiwzL/0Xk4vJ7fl0nLJx0w81WHb4ccZ/CsJ89M=              signType: RSA              version: '1.0'      responses:        '200':          description: ''          content:            application/json:              schema:                type: object                properties:                  charset:                    type: string                  data:                    type: object                    properties:                      code:                        type: string                        description: SUCCESS                        title: 成功code                      msg:                        type: string                        title: 描述                        description: 成功                      payWayType:                        type: integer                        description: 支付方式 与入参相同                      polymerizationNo:                        type: string                        title: 业务订单                      tradeNo:                        type: string                        title: 交易流水号                        description: 交易流水号                    required:                      - code                      - msg                      - payWayType                      - polymerizationNo                      - tradeNo                    x-apifox-orders:                      - code                      - msg                      - payWayType                      - polymerizationNo                      - tradeNo                  msgCode:                    type: string                    title: '0'                    description: 接口联通code 除0外都为异常                  sign:                    type: string                    description: 签名                  signType:                    type: string                    description: 固定值RSA                required:                  - charset                  - data                  - msgCode                  - sign                  - signType                x-apifox-orders:                  - charset                  - data                  - msgCode                  - sign                  - signType                description: UTF-8                title: 编码              example:                charset: UTF-8                data:                  code: SUCCESS                  msg: 成功                  payWayType: 1101                  polymerizationNo: '123456789'                  tradeNo: '123456789'                msgCode: '0'                sign: >-                  gnyoMFE+xJXZfo1MtVQ8mK6ZzLcJwZvd//qujfcPeVQuIdXq6QY4Gyb6Ortmj4E0HuLW+5A7B/pEHZFkNma5OHRzlhu9qwIUcRXABKj1n/L25V9G/tNWV1awMHpsXoBqvHZ2c6h6dPeL4OrwsMTKM9M0dta5jJVDEAmCkeWXmhw=                signType: RSA          headers: {}          x-apifox-name: 成功      security: []      x-apifox-folder: 支付接口      x-apifox-status: developing      x-run-in-apifox: https://app.apifox.com/web/project/2819878/apis/api-86163935-run components:  schemas: {}  securitySchemes: {} servers:  - url: http://test-cn.your-api-server.com    description: 测试环境 security: [] ```



\# 支付查询接口 ## OpenAPI Specification ```yaml openapi: 3.0.1 info:  title: ''  description: ''  version: 1.0.0 paths:  /realpay/gateway:    post:      summary: 支付查询接口      deprecated: false      description: ''      tags:        - 功能接口      parameters: []      requestBody:        content:          application/json:            schema:              type: object              properties:                charset:                  type: string                  title: 编码格式                  description: UTF-8                method:                  type: string                  title: 方法名                  description: rop.trade.query                reqDate:                  type: string                  title: 请求时间                  description: '20230410175544'                appId:                  type: string                  title: appid                  description: appId (平台统一下发)                bizContent:                  type: object                  properties:                    orderNo:                      type: string                      title: 订单号                      description: 商户订单号（支付时使用的orderNo）                  x-apifox-orders:                    - orderNo                  required:                    - orderNo                  description: 请求参数的集合，json字符串形式                sign:                  type: string                  title: 签名                signType:                  type: string                  description: 固定RSA                  title: 签名code                version:                  type: string                  title: 版本号                  description: '1.0'              required:                - charset                - method                - reqDate                - appId                - bizContent                - sign                - signType                - version              x-apifox-orders:                - appId                - method                - reqDate                - bizContent                - sign                - charset                - signType                - version            example:              charset: UTF-8              method: rop.trade.query              reqDate: '20230410175544'              appId: 测试              bizContent: '{"orderNo":"gtxchuangs0013"}'              sign: >-                bWDJbcar5hl4osyxLf1r7Rz7FN8ke+tOqrGpVB4wY3Aaz5N7HjIrZ/2ha74PBGnhyf9pBKsrH+YnL9CgQP6uYLG7stJ67lZNaRgbmgt2gkXSR88A2YOW/rMjpiULCiRGaiaRQzqAyksCN2ViJohor4oylGtXedWAj69rF70mYpU=              signType: RSA              version: '1.0'      responses:        '200':          description: ''          content:            application/json:              schema:                type: object                properties:                  charset:                    type: string                    title: 编码格式                    description: UTF-8                  data:                    type: object                    properties:                      code:                        type: string                        title: 业务code                      msg:                        type: string                        title: 业务描述                      payWayType:                        type: integer                        title: 支付类型                      polymerizationNo:                        type: string                        title: 业务订单号                        description: 医保、银行第三方等特殊业务会有                      tradeNo:                        type: string                        title: 交易流水号                    required:                      - code                      - msg                      - payWayType                      - polymerizationNo                      - tradeNo                    x-apifox-orders:                      - code                      - msg                      - payWayType                      - polymerizationNo                      - tradeNo                    title: 业务参数集合                  msgCode:                    type: string                  sign:                    type: string                    title: 签名                  signType:                    type: string                    title: 签名code                    description: 固定值RSA                  01H1X92ZRAKPK5FJJ27T4YP8P1:                    type: string                required:                  - charset                  - data                  - msgCode                  - sign                  - signType                  - 01H1X92ZRAKPK5FJJ27T4YP8P1                x-apifox-orders:                  - 01H1X92ZRAKPK5FJJ27T4YP8P1                  - charset                  - data                  - msgCode                  - sign                  - signType              example:                charset: UTF-8                data:                  code: SUCCESS                  msg: 成功                  payWayType: 1101                  polymerizationNo: '1012023060211263638217597'                  tradeNo: '4200059248202306026475068785'                msgCode: '0'                sign: >-                  KdnbAN7RsI55FJS/0bVphcRyOWW+sHRPdBBoAEM5rxp03JlfgBQlHeH6/DTDV5XcnWovvGbTLwskEWosAHFgaK0x9hruHUQVeCrtDsdqF8eiQelDqW4WWkhK7smhDzZtJ7hhOjg2IaOXLKnlwMKllx5xAcnpL81aQ7lfDvqN2/w=                signType: RSA          headers: {}          x-apifox-name: 成功      security: []      x-apifox-folder: 功能接口      x-apifox-status: developing      x-run-in-apifox: https://app.apifox.com/web/project/2819878/apis/api-86164896-run components:  schemas: {}  securitySchemes: {} servers:  - url: http://test-cn.your-api-server.com    description: 测试环境 security: [] ```



\# 支付查询接口 ## OpenAPI Specification ```yaml openapi: 3.0.1 info:  title: ''  description: ''  version: 1.0.0 paths:  /realpay/gateway:    post:      summary: 支付查询接口      deprecated: false      description: ''      tags:        - 功能接口      parameters: []      requestBody:        content:          application/json:            schema:              type: object              properties:                charset:                  type: string                  title: 编码格式                  description: UTF-8                method:                  type: string                  title: 方法名                  description: rop.trade.query                reqDate:                  type: string                  title: 请求时间                  description: '20230410175544'                appId:                  type: string                  title: appid                  description: appId (平台统一下发)                bizContent:                  type: object                  properties:                    orderNo:                      type: string                      title: 订单号                      description: 商户订单号（支付时使用的orderNo）                  x-apifox-orders:                    - orderNo                  required:                    - orderNo                  description: 请求参数的集合，json字符串形式                sign:                  type: string                  title: 签名                signType:                  type: string                  description: 固定RSA                  title: 签名code                version:                  type: string                  title: 版本号                  description: '1.0'              required:                - charset                - method                - reqDate                - appId                - bizContent                - sign                - signType                - version              x-apifox-orders:                - appId                - method                - reqDate                - bizContent                - sign                - charset                - signType                - version            example:              charset: UTF-8              method: rop.trade.query              reqDate: '20230410175544'              appId: 测试              bizContent: '{"orderNo":"gtxchuangs0013"}'              sign: >-                bWDJbcar5hl4osyxLf1r7Rz7FN8ke+tOqrGpVB4wY3Aaz5N7HjIrZ/2ha74PBGnhyf9pBKsrH+YnL9CgQP6uYLG7stJ67lZNaRgbmgt2gkXSR88A2YOW/rMjpiULCiRGaiaRQzqAyksCN2ViJohor4oylGtXedWAj69rF70mYpU=              signType: RSA              version: '1.0'      responses:        '200':          description: ''          content:            application/json:              schema:                type: object                properties:                  charset:                    type: string                    title: 编码格式                    description: UTF-8                  data:                    type: object                    properties:                      code:                        type: string                        title: 业务code                      msg:                        type: string                        title: 业务描述                      payWayType:                        type: integer                        title: 支付类型                      polymerizationNo:                        type: string                        title: 业务订单号                        description: 医保、银行第三方等特殊业务会有                      tradeNo:                        type: string                        title: 交易流水号                    required:                      - code                      - msg                      - payWayType                      - polymerizationNo                      - tradeNo                    x-apifox-orders:                      - code                      - msg                      - payWayType                      - polymerizationNo                      - tradeNo                    title: 业务参数集合                  msgCode:                    type: string                  sign:                    type: string                    title: 签名                  signType:                    type: string                    title: 签名code                    description: 固定值RSA                  01H1X92ZRAKPK5FJJ27T4YP8P1:                    type: string                required:                  - charset                  - data                  - msgCode                  - sign                  - signType                  - 01H1X92ZRAKPK5FJJ27T4YP8P1                x-apifox-orders:                  - 01H1X92ZRAKPK5FJJ27T4YP8P1                  - charset                  - data                  - msgCode                  - sign                  - signType              example:                charset: UTF-8                data:                  code: SUCCESS                  msg: 成功                  payWayType: 1101                  polymerizationNo: '1012023060211263638217597'                  tradeNo: '4200059248202306026475068785'                msgCode: '0'                sign: >-                  KdnbAN7RsI55FJS/0bVphcRyOWW+sHRPdBBoAEM5rxp03JlfgBQlHeH6/DTDV5XcnWovvGbTLwskEWosAHFgaK0x9hruHUQVeCrtDsdqF8eiQelDqW4WWkhK7smhDzZtJ7hhOjg2IaOXLKnlwMKllx5xAcnpL81aQ7lfDvqN2/w=                signType: RSA          headers: {}          x-apifox-name: 成功      security: []      x-apifox-folder: 功能接口      x-apifox-status: developing      x-run-in-apifox: https://app.apifox.com/web/project/2819878/apis/api-86164896-run components:  schemas: {}  securitySchemes: {} servers:  - url: http://test-cn.your-api-server.com    description: 测试环境 security: [] ```



\# 退费接口 ## OpenAPI Specification ```yaml openapi: 3.0.1 info:  title: ''  description: ''  version: 1.0.0 paths:  /realpay/gateway:    post:      summary: 退费接口      deprecated: false      description: ''      tags:        - 功能接口      parameters: []      requestBody:        content:          application/json:            schema:              type: object              properties:                appId:                  type: string                  description: appId (平台统一下发)                  title: appid                method:                  type: string                  description: rop.trade.refund                  title: 方法名                version:                  type: string                  title: 版本号                charset:                  type: string                  title: 编码格式                  description: 固定值 UTF-8                signType:                  type: string                  title: 签名code                sign:                  type: string                  title: 签名                bizContent:                  type: object                  properties:                    orderNo:                      type: string                      title: 订单号                      description: 商户订单号（支付时使用的orderNo）                    batchNo:                      type: string                      title: 退款批次号                      description: 一笔订单可多次退费 每次需变更批次号                    refundFee:                      type: string                      title: 退费金额                      description: '1.23'                    tradeDesc:                      type: string                      title: 退费描述                      description: 例：窗口退费                    notifyUrl:                      type: string                      title: 回调地址                      description: 无特殊需求可不传                      nullable: true                    operatorName:                      type: string                      title: 操作员                      description: 小明                      nullable: true                    operatorCode:                      type: string                      title: 操作员code                      description: A0001                      nullable: true                  title: 业务参数                  x-apifox-orders:                    - orderNo                    - batchNo                    - refundFee                    - tradeDesc                    - notifyUrl                    - operatorName                    - operatorCode                  required:                    - orderNo                    - batchNo                    - refundFee                    - tradeDesc                  description: 请求参数的集合，json字符串形式              required:                - appId                - method                - version                - charset                - signType                - sign                - bizContent              x-apifox-orders:                - appId                - method                - version                - charset                - signType                - sign                - bizContent            example:              appId: tzzy2093901              method: rop.trade.refund              version: '1.0'              charset: UTF-8              signType: RSA              sign: >-                O2BrQxGZLsuNt9NXNNFAzv10NlgABWavUjlEG/hLcf/Zoy20cTR+Aa3xwDu8Z9M5FfBKbw24NdfBmaQ0ZCHVZv0FvoWlNMhbZHlFtVyOWjMV0nT87ihPdWAedWs/JqGUMJVy/YTmyUzIxBrupKTDQvUZDYeEct+GgharMoCDHfg=              bizContent: >-                {"orderNo":"2305060936387225542297051003","tradeNo":"","batchNo":"123","cancelSerialNo":"","refundFee":3.34,"tradeDesc":"互联网医院退款","notifyUrl":"","recipeNos":"","operatorName":"赵凯","operatorCode":"921309"}      responses:        '200':          description: ''          content:            application/json:              schema:                type: object                properties:                  charset:                    type: string                    title: 固定                    description: UTF-8                  data:                    type: object                    properties:                      code:                        type: string                        title: 退款结果code                        description: ' SUCCESS和ACCEPTSUCCESS均代表成功，其余都为失败code'                      msg:                        type: string                        title: 退费描述                        description: 成功                      outRefundNo:                        type: string                        title: 退费订单号                      tradeNo:                        type: string                        title: 交易流水号                    required:                      - code                      - msg                      - outRefundNo                      - tradeNo                    x-apifox-orders:                      - code                      - msg                      - outRefundNo                      - tradeNo                    title: 业务参数                  msgCode:                    type: string                    title: 接口成功code                    description: '例：0     非0data会为空 '                  sign:                    type: string                    title: 签名                  signType:                    type: string                    title: 签名code                    description: RSA                required:                  - charset                  - data                  - msgCode                  - sign                  - signType                x-apifox-orders:                  - charset                  - data                  - msgCode                  - sign                  - signType              example:                charset: UTF-8                data:                  code: ACCEPTSUCCESS                  msg: OK                  outRefundNo: '50300005942023060235204855780'                  tradeNo: '4200059249202306013551003069'                msgCode: '0'                sign: >-                  XPFYho6RZRLRPJs4NOArOD90MEz49wa6zkruHrfDN5mpgVuvroGLAlaX+F6P8GDSFbYFtAiY9Ad4RBX4lXpZWnPbigD7NXWWJjQmxyD/37lnEb0bhdfA8NsOgpwmtentnFmu53GLSNBJFdj76YQoSulJmocbLE1kDo94zofsA9w=                signType: RSA          headers: {}          x-apifox-name: 成功      security: []      x-apifox-folder: 功能接口      x-apifox-status: developing      x-run-in-apifox: https://app.apifox.com/web/project/2819878/apis/api-86184827-run components:  schemas: {}  securitySchemes: {} servers:  - url: http://test-cn.your-api-server.com    description: 测试环境 security: [] ```

\# 撤销接口 ## OpenAPI Specification ```yaml openapi: 3.0.1 info:  title: ''  description: ''  version: 1.0.0 paths:  /realpay/gateway:    post:      summary: 撤销接口      deprecated: false      description: |-        条码付、扫码付款可用        注意！！撤销会退费！！慎用！！！      tags:        - 功能接口      parameters: []      requestBody:        content:          application/json:            schema:              type: object              properties:                charset:                  type: string                  title: 编码格式                  description: UTF-8                method:                  type: string                  description: rop.trade.cancel                  title: 方法名                reqDate:                  type: string                  title: 时间                appId:                  type: string                  description: appId (平台统一下发)                  title: appid                bizContent:                  type: object                  properties:                    orderNo:                      type: string                      title: 业务订单号                  x-apifox-orders:                    - orderNo                  title: 业务集合                  required:                    - orderNo                sign:                  type: string                  title: 签名code                signType:                  type: string                  title: 签名code                  description: RSA                version:                  type: string                  title: 版本号              required:                - charset                - method                - reqDate                - appId                - bizContent                - sign                - signType                - version              x-apifox-orders:                - appId                - method                - reqDate                - bizContent                - charset                - sign                - signType                - version            example:              charset: UTF-8              method: rop.trade.cancel              reqDate: '20230410175544'              appId: 测试              bizContent: '{"orderNo":"gtxchuangs0013"}'              sign: >-                bWDJbcar5hl4osyxLf1r7Rz7FN8ke+tOqrGpVB4wY3Aaz5N7HjIrZ/2ha74PBGnhyf9pBKsrH+YnL9CgQP6uYLG7stJ67lZNaRgbmgt2gkXSR88A2YOW/rMjpiULCiRGaiaRQzqAyksCN2ViJohor4oylGtXedWAj69rF70mYpU=              signType: RSA              version: '1.0'      responses:        '200':          description: ''          content:            application/json:              schema:                type: object                properties:                  charset:                    type: string                    title: 编码格式                    description: UTF-8                  data:                    type: object                    properties:                      code:                        type: string                        title: 业务code                        description: SUCEESS                      msg:                        type: string                        title: 描述                    required:                      - code                      - msg                    x-apifox-orders:                      - code                      - msg                    title: 业务集合                  msgCode:                    type: string                    title: 接口code                    description: '0'                  sign:                    type: string                    title: 签名                  signType:                    type: string                    title: 签名code                  batchNo:                    type: string                    title: 退费批次号                    description: 若没有发生退费 不会返回                required:                  - charset                  - data                  - msgCode                  - sign                  - signType                  - batchNo                x-apifox-orders:                  - charset                  - data                  - msgCode                  - batchNo                  - sign                  - signType              example:                charset: UTF-8                data:                  code: SUCCESS                  msg: OK                msgCode: '0'                sign: XPFYho6RZRLRPJs4NOArOD90MEz49wa6zkruHrfDN5mpgVuvro...                signType: RSA          headers: {}          x-apifox-name: 成功      security: []      x-apifox-folder: 功能接口      x-apifox-status: developing      x-run-in-apifox: https://app.apifox.com/web/project/2819878/apis/api-86325532-run components:  schemas: {}  securitySchemes: {} servers:  - url: http://test-cn.your-api-server.com    description: 测试环境 security: [] ```



\# 账单下载接口 ## OpenAPI Specification ```yaml openapi: 3.0.1 info:  title: ''  description: ''  version: 1.0.0 paths:  /payment/downloadChannelBills:    post:      summary: 账单下载接口      deprecated: false      description: 提供支付平台账单，返回参数可根据实际情况添加      tags:        - 功能接口      parameters:        - name: channelType          in: query          description: 0 全部  1支付宝 2 微信 .....          required: false          example: '0'          schema:            type: string        - name: billDate          in: query          description: 账单日期          required: false          example: '20230303'          schema:            type: string        - name: pageNo          in: query          description: ''          required: false          example: '1'          schema:            type: string        - name: pageSize          in: query          description: ''          required: false          example: '100'          schema:            type: string      responses:        '200':          description: ''          content:            application/json:              schema:                type: object                properties:                  msgCode:                    type: string                    title: 接口code                  msg:                    type: string                    title: 描述                  charset:                    type: string                    title: 固定值                  signType:                    type: string                  sign:                    type: string                    title: 签名                  data:                    type: object                    properties:                      channelType:                        type: integer                      pageInfo:                        type: object                        properties:                          pageNo:                            type: integer                          pageSize:                            type: integer                          total:                            type: 'null'                        required:                          - pageNo                          - pageSize                          - total                        x-apifox-orders:                          - pageNo                          - pageSize                          - total                      billList:                        type: array                        items:                          type: object                          properties:                            userName:                              type: string                              title: 归集标识                            orderNo:                              type: string                              title: 订单号                            outTradeNo:                              type: 'null'                              title: 外部订单号                            orderType:                              type: integer                              title: 订单类型                            payWayType:                              type: integer                              title: 支付类型                            tradeSeq:                              type: string                            tradeType:                              type: integer                              title: 支付类型                              description: 1支付 2退费                            tradeFee:                              type: string                              title: 交易金额                            tradeStatus:                              type: integer                              title: 交易状态                              description: 2 已支付 4已退费                            tradeDesc:                              type: string                              title: 交易描述                            tradeNo:                              type: string                              title: 交易流水号                          x-apifox-orders:                            - userName                            - orderNo                            - outTradeNo                            - orderType                            - payWayType                            - tradeSeq                            - tradeType                            - tradeFee                            - tradeStatus                            - tradeDesc                            - tradeNo                        title: 订单列表                    required:                      - channelType                      - pageInfo                      - billList                    x-apifox-orders:                      - channelType                      - pageInfo                      - billList                    title: 数据集合                required:                  - msgCode                  - msg                  - charset                  - signType                  - sign                  - data                x-apifox-orders:                  - msgCode                  - msg                  - charset                  - signType                  - sign                  - data              example:                msgCode: '0'                msg: null                charset: null                signType: null                sign: null                data:                  channelType: 0                  pageInfo:                    pageNo: 1                    pageSize: 10000                    total: null                  billList:                    - id: null                      userName: kmyd80027                      orderNo: MH1080027230417999009                      outTradeNo: null                      orderType: 2                      payWayType: 35                      amount: null                      billStatus: null                      outTime: null                      reqCount: 0                      generateTime: null                      version: null                      enable: null                      createBy: null                      createDate: null                      updateBy: null                      updateDate: null                      orderTradeId: null                      orderId: null                      tradeSeq: MH1080027230417999009R0                      tradeType: 1                      tradeFee: '10.50'                      tradeStatus: 2                      tradeTime: null                      checkStatus: null                      checkTime: null                      tradeDesc: 挂号订单 就诊卡号：**********                      notifyTime: null                      notifyResult: 0                      notifyDesc: null                      tradeNo: '4200001822202304171216077454'                      settleDate: 1681745119000                      isHisReq: null                      retryTimes: null                      tryTime: null                      pushStatus: 0                      pushTimes: null                      tradeVersion: null                      tradeEnable: null                      tradeCreateBy: null                      tradeCreateDate: 1681744990000                      tradeUpdateBy: null                      tradeUpdateDate: null                      payWay: null                      merName: 昆明医                      patientName: 徐兴磊                      markNo: '**********'                      caseNo: null                      refundPayTime: null                      clinicCode: '**********'                      inpatientCode: null                      inpatientNo: null                      outRefundNo: null          headers: {}          x-apifox-name: 成功      security: []      x-apifox-folder: 功能接口      x-apifox-status: developing      x-run-in-apifox: https://app.apifox.com/web/project/2819878/apis/api-86562854-run components:  schemas: {}  securitySchemes: {} servers:  - url: http://test-cn.your-api-server.com    description: 测试环境 security: [] ```



\# 获取支付平台appid ## OpenAPI Specification ```yaml openapi: 3.0.1 info:  title: ''  description: ''  version: 1.0.0 paths:  /info/query/queryAppId:    post:      summary: 获取支付平台appid      deprecated: false      description: 针对医院多个商户或渠道，退费查询appid      tags:        - 功能接口      parameters:        - name: orderNo          in: query          description: ''          required: true          example: ZZJ01420230720220748,          schema:            type: string      responses:        '200':          description: ''          content:            application/json:              schema:                type: object                properties:                  msgCode:                    type: string                    title: code                  data:                    type: string                    title: 所用appid                x-apifox-orders:                  - msgCode                  - data                required:                  - msgCode                  - data              example:                msgCode: '0'                msg: null                charset: null                signType: null                sign: null                data: zkfy8015203          headers: {}          x-apifox-name: 成功      security: []      x-apifox-folder: 功能接口      x-apifox-status: developing      x-run-in-apifox: https://app.apifox.com/web/project/2819878/apis/api-105747367-run components:  schemas: {}  securitySchemes: {} servers:  - url: http://test-cn.your-api-server.com    description: 测试环境 security: [] ```



\# 签名规则  1.请求参数按照key=value&key=value方式拼接的未签名原始字符串，拼接key按字典排序。 ::: highlight red 📌 appId=2152089221&bizContent={"orderNo":"20170948000111331","payWayType":"22","orderType":"1"}&charset=utf-8&method= rop.trade.pay&signType=RSA&version=1.0 ::: 2.再对原始字符串进行签名（用商户私钥）获取sign ::: highlight red 📌 sign=cYmuUnKi5QdBsoZEAbMXVMmRWjsuUj+y48A2DvWAVVBuYkiBj13CFDHu2vZQvmOfkjE0YqCUQE04kqm9Xg3tIX8tPeIGIFtsIyp/M45w1ZsDOiduBbduGfRo1XRsvAyVAv2hCrBLLrDI5Vi7uZZ77Lo5J0PpUUWwyQGt0M4cj8g= ::: 3.最后对请求josn的sign值进行encode转码（如果获取sign的方法已经做encode，就不需要encode编码），格式按请求串中的UTF-8，获得最终的请求josn  ::: highlight red 💡 { “appId”: “2152089221”, “method”: “rop.trade.pay”, “version”: “1.O”, “charset”: “UTF-8”, “signType”: “RSA”,	 “bizContent”: “{‘orderNo’:  ‘201709160001123’}”， “sign”：“XHISDAHD=/SDAHDSAISDHDAUDPQUUFGSDOAGAUGD121IDUSDBD”	 } ::: 



\# 支付流程  

   ![gggggg.pic.jpg](https://api.apifox.cn/api/v1/projects/2819878/resources/385357/image-preview)





\# 对账试图标准  # **对账试图标准**  ![image.png](https://api.apifox.cn/api/v1/projects/2819878/resources/385527/image-preview) ::: highlight red 💡 试图中字段值可空，字段必须有。 常用枚举不清楚可询问支付平台。 :::    ::: note 常用支付方式枚举： 1:支付宝 2:微信 3:银联 9:微信医保 28:支付宝医保 :::  ::: note 常用业务枚举： 1:住院 2:挂号 4:处方缴费 10:病案复印 :::