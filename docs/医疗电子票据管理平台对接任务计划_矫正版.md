# 医疗电子票据管理平台对接任务计划（矫正版）

## 📋 项目概述

### 项目背景
基于现有体检管理系统与《医疗电子票据管理平台-接口规范vsaas2.0--内蒙古自治区人民医院20250630》文档要求，实现电子票据管理功能的完整对接。

### 核心业务场景
1. **个人体检**：支付完成即时开具电子票据
2. **团检业务**：支持预开票据，后续统一结算的复杂场景  
3. **退费处理**：支持全额冲红和部分冲红
4. **统一管理**：提供集中化的票据管理界面
5. **审计合规**：完整的票据审计轨迹和合规性监控

### 技术架构特点
- **支付架构**：支付单(CustomerRegBill) → 多个支付记录(FeePayRecord)
- **团检支持**：后付费标志(afterPayFlag)实现预开票据功能
- **接口追踪**：基于InterfaceTraceLog的完整日志体系
- **状态管理**：支持复杂的票据状态流转和同步
- **审计体系**：全链条票据审计轨迹，支持合规性检查和风险监控

### 实际接口规范对接
根据电子发票接口.md文档，主要接口包括：
- **门诊电子票据开具接口**：`invoiceEBillOutpatient`
- **住院电子票据开具接口**：`invEBillHospitalized`
- **电子票据冲红接口**：`writeOffEBill`
- **电子票据状态查询接口**：`getEBillStatesByBillInfo`

---

## 🎯 任务清单

### 🔥 高优先级任务

#### 1. 分析电子票据接口规范文档，定义API接口契约
**任务编号：** VS-001  
**预计工期：** 3天  
**负责人：** 系统架构师 + 业务分析师

**任务详情：**
- 深度解析电子发票接口.md规范文档
- 理解门诊/住院开票、状态查询、冲红等核心接口
- 分析团检预开票据的特殊业务场景
- 设计接口安全认证方案（数字签名、Token等）

**交付物：**
- 接口规范解析报告
- API接口设计文档  
- 数据传输格式定义
- 错误码映射表
- 接口安全方案

**技术要点：**
```java
// 基于实际接口规范的服务定义
public interface ElectronicBillApiService {
    /**
     * 门诊电子票据开具接口
     * @param request 门诊开票请求
     * @return 开票结果
     */
    ElectronicBillResponse invoiceOutpatient(OutpatientBillRequest request);
    
    /**
     * 住院电子票据开具接口  
     * @param request 住院开票请求
     * @return 开票结果
     */
    ElectronicBillResponse invoiceHospitalized(HospitalizedBillRequest request);
    
    /**
     * 电子票据冲红接口
     * @param request 冲红请求
     * @return 冲红结果
     */
    WriteOffResponse writeOffBill(WriteOffRequest request);
    
    /**
     * 根据电子票信息获取电子票据状态接口
     * @param billBatchCode 电子票据代码
     * @param billNo 电子票据号码
     * @return 票据状态
     */
    BillStatusResponse getBillStatus(String billBatchCode, String billNo);
}
```

#### 2. 创建电子票据管理实体类和数据表结构
**任务编号：** VS-002  
**预计工期：** 2天  
**负责人：** 后端开发工程师

**任务详情：**
- 设计电子票据核心实体类
- 创建数据库表结构和索引
- 建立实体间的关联关系
- 编写数据访问层代码

**数据表结构设计：**

**1. 电子票据主表 (electronic_bill)**
```sql
CREATE TABLE `electronic_bill` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `bill_id` varchar(32) NOT NULL COMMENT '关联支付单ID',
  `bill_no` varchar(50) DEFAULT NULL COMMENT '支付单号',
  `bill_batch_code` varchar(50) DEFAULT NULL COMMENT '电子票据代码',
  `electronic_bill_no` varchar(20) DEFAULT NULL COMMENT '电子票据号码',
  `random_code` varchar(20) DEFAULT NULL COMMENT '电子校验码',
  `bus_no` varchar(50) NOT NULL COMMENT '业务流水号',
  `bus_type` varchar(20) NOT NULL COMMENT '业务标识：02-门诊,01-住院',
  `payer` varchar(100) NOT NULL COMMENT '患者姓名',
  `bus_date_time` varchar(17) NOT NULL COMMENT '业务发生时间',
  `place_code` varchar(50) NOT NULL COMMENT '开票点编码',
  `payee` varchar(50) NOT NULL COMMENT '收费员',
  `author` varchar(100) NOT NULL COMMENT '票据编制人',
  `checker` varchar(100) DEFAULT NULL COMMENT '票据复核人',
  `total_amt` decimal(14,2) NOT NULL COMMENT '开票总金额',
  `bill_status` varchar(1) DEFAULT '1' COMMENT '状态：1正常，2作废',
  `is_scarlet` varchar(1) DEFAULT '0' COMMENT '是否已开红票：0未开红票，1已开红票',
  `ivc_date_time` varchar(17) DEFAULT NULL COMMENT '开票时间',
  `bill_qr_code` text DEFAULT NULL COMMENT '电子票据二维码图片数据',
  `picture_url` varchar(500) DEFAULT NULL COMMENT '电子票据H5页面URL',
  `picture_net_url` varchar(500) DEFAULT NULL COMMENT '电子票据外网H5页面URL',
  `team_flag` int(1) DEFAULT '0' COMMENT '团检标志：1-团检，0-个检',
  `pre_issue_flag` int(1) DEFAULT '0' COMMENT '预开票标志：1-预开票，0-正常开票',
  `company_name` varchar(200) DEFAULT NULL COMMENT '团检单位名称',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_bill_id` (`bill_id`),
  KEY `idx_electronic_bill_no` (`electronic_bill_no`),
  KEY `idx_bus_no` (`bus_no`),
  KEY `idx_bill_batch_code` (`bill_batch_code`),
  KEY `idx_team_flag` (`team_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='电子票据主表';
```

**2. 电子票据明细表 (electronic_bill_detail)**
```sql
CREATE TABLE `electronic_bill_detail` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `bill_id` varchar(32) NOT NULL COMMENT '电子票据ID',
  `pay_record_id` varchar(32) DEFAULT NULL COMMENT '支付记录ID',
  `item_group_id` varchar(32) DEFAULT NULL COMMENT '项目组合ID',
  `code` varchar(50) NOT NULL COMMENT '项目编码',
  `name` varchar(200) NOT NULL COMMENT '项目名称',
  `standard` varchar(100) DEFAULT NULL COMMENT '规格',
  `unit` varchar(20) DEFAULT NULL COMMENT '单位',
  `price` decimal(10,4) NOT NULL COMMENT '单价',
  `amt` decimal(14,2) NOT NULL COMMENT '金额',
  `med_care_institution` varchar(50) DEFAULT NULL COMMENT '医保报销类型',
  `balanced_number` varchar(50) DEFAULT NULL COMMENT '费款所属期',
  `exec_dept_name` varchar(100) DEFAULT NULL COMMENT '执行科室',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_bill_id` (`bill_id`),
  KEY `idx_pay_record_id` (`pay_record_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='电子票据明细表';
```

**3. 电子票据冲红记录表 (electronic_bill_writeoff)**
```sql  
CREATE TABLE `electronic_bill_writeoff` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `original_bill_id` varchar(32) NOT NULL COMMENT '原电子票据ID',
  `original_bill_batch_code` varchar(50) NOT NULL COMMENT '原电子票据代码',
  `original_bill_no` varchar(20) NOT NULL COMMENT '原电子票据号码',
  `scarlet_bill_batch_code` varchar(50) DEFAULT NULL COMMENT '电子红票代码',
  `scarlet_bill_no` varchar(20) DEFAULT NULL COMMENT '电子红票号码',
  `scarlet_random` varchar(20) DEFAULT NULL COMMENT '电子红票校验码',
  `reason` varchar(200) NOT NULL COMMENT '冲红原因',
  `operator` varchar(60) NOT NULL COMMENT '经办人',
  `bus_date_time` varchar(17) NOT NULL COMMENT '业务发生时间',
  `place_code` varchar(50) NOT NULL COMMENT '开票点编码',
  `create_time` varchar(17) DEFAULT NULL COMMENT '电子红票生成时间',
  `bill_qr_code` text DEFAULT NULL COMMENT '电子红票二维码图片数据',
  `picture_url` varchar(500) DEFAULT NULL COMMENT '电子红票H5页面URL',
  `picture_net_url` varchar(500) DEFAULT NULL COMMENT '电子红票外网H5页面URL',
  `status` varchar(1) DEFAULT '1' COMMENT '冲红状态：1-成功，0-失败',
  `error_message` varchar(1000) DEFAULT NULL COMMENT '错误消息',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_original_bill_id` (`original_bill_id`),
  KEY `idx_original_bill_no` (`original_bill_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='电子票据冲红记录表';
```

#### 3. 扩展CustomerRegBill实体，增加电子票据相关字段
**任务编号：** VS-003  
**预计工期：** 1天  
**负责人：** 后端开发工程师

**任务详情：**
- 在CustomerRegBill实体中增加电子票据关联字段
- 更新数据库表结构
- 修改相关Mapper和Service方法

**新增字段：**
```java
// 电子票据相关字段
private String electronicBillBatchCode;  // 电子票据代码
private String electronicBillNo;         // 电子票据号码
private String electronicBillRandom;     // 电子校验码
private String electronicBillStatus;     // 电子票据状态：1正常，2作废
private String electronicBillQrCode;     // 电子票据二维码
private String electronicBillPictureUrl; // 电子票据H5页面URL
private Date electronicBillIvcTime;      // 开票时间
private String electronicBillError;      // 开票错误信息
```

#### 4. 实现支付单级别的电子票据申请接口
**任务编号：** VS-004  
**预计工期：** 5天  
**负责人：** 后端开发工程师

**任务详情：**
- 实现门诊电子票据开具接口调用
- 实现住院电子票据开具接口调用
- 支持团检和个人两种业务场景
- 完善错误处理和重试机制
- 集成签名验证和安全认证

**核心服务实现：**
```java
@Service
public class ElectronicBillService {
    
    /**
     * 门诊电子票据开具
     */
    public ElectronicBillResponse createOutpatientBill(CustomerRegBill bill) {
        // 1. 构建开票请求参数
        OutpatientBillRequest request = buildOutpatientRequest(bill);
        
        // 2. 调用电子票据平台接口
        ElectronicBillResponse response = billApiClient.invoiceOutpatient(request);
        
        // 3. 处理返回结果并更新本地数据
        handleBillResponse(bill, response);
        
        return response;
    }
    
    /**
     * 住院电子票据开具  
     */
    public ElectronicBillResponse createHospitalizedBill(CustomerRegBill bill) {
        // 类似实现
    }
}
```

#### 5. 实现团检预开票据功能(后付费场景)
**任务编号：** VS-005  
**预计工期：** 4天  
**负责人：** 后端开发工程师

**任务详情：**
- 支持团检afterPayFlag=1的预开票场景
- 实现预开票据与正式结算的关联
- 提供团检票据批量管理功能
- 完善团检票据状态追踪

**关键业务逻辑：**
```java
/**
 * 团检预开票据处理
 */
public void handleTeamPreIssueBill(CustomerRegBill bill) {
    if ("1".equals(bill.getAfterPayFlag())) {
        // 预开票逻辑：先开票，后付费
        ElectronicBillResponse response = createPreIssueBill(bill);
        // 设置预开票标识
        bill.setPreIssueFlag(1);
        // 等待后续付费确认
    } else {
        // 正常开票流程
        createNormalBill(bill);
    }
}
```

#### 6. 创建统一电子发票管理页面和控制器
**任务编号：** VS-006  
**预计工期：** 4天  
**负责人：** 前端+后端开发工程师

**任务详情：**
- 创建电子票据管理控制器
- 实现票据查询、状态更新、冲红等API
- 设计统一的票据管理前端界面
- 支持团检票据批量操作

#### 7. 设计和实现电子票据审计体系
**任务编号：** VS-007  
**预计工期：** 3天  
**负责人：** 后端开发工程师

**任务详情：**
- 设计审计日志表结构
- 实现操作审计记录
- 提供审计查询和统计功能
- 支持合规性检查

### 🔶 中优先级任务

#### 8. 完善InterfaceTraceLog，增加票据接口追踪
**任务编号：** VS-008  
**预计工期：** 2天

#### 9. 实现电子票据状态查询和同步机制  
**任务编号：** VS-009  
**预计工期：** 3天

#### 10. 实现电子票据冲红/作废接口(支持部分冲红)
**任务编号：** VS-010  
**预计工期：** 4天

#### 11. 前端增加电子票据管理界面和团检票据管理
**任务编号：** VS-011  
**预计工期：** 5天

#### 12. 实现发票批量操作功能(批量申请、批量冲红、批量下载)
**任务编号：** VS-012  
**预计工期：** 3天

#### 13. 实现电子票据审计报告和监控告警功能
**任务编号：** VS-013  
**预计工期：** 4天

### 🔹 低优先级任务

#### 14. 实现票据数据统计和团检财务报表功能
**任务编号：** VS-014  
**预计工期：** 3天

---

## 📅 项目时间规划

### 第一阶段 (Week 1-2): 基础架构搭建
- 完成任务1-3：接口分析、实体设计、数据表创建

### 第二阶段 (Week 3-4): 核心功能开发  
- 完成任务4-5：支付单级别开票、团检预开票

### 第三阶段 (Week 5-6): 管理功能开发
- 完成任务6-7：管理页面、审计体系

### 第四阶段 (Week 7-8): 完善和优化
- 完成中低优先级任务：状态同步、冲红、批量操作等

---

## 🔧 技术要求

### 开发环境
- Java 8+
- Spring Boot 2.7.x
- MyBatis-Plus
- Vue 3 + Ant Design Vue
- MySQL 8.0+

### 接口规范遵循
- 严格按照电子发票接口.md规范实现
- 支持数字签名和安全认证
- 完善的错误处理和日志记录
- 支持接口重试和容错机制

### 代码质量要求
- 单元测试覆盖率 > 80%
- 代码注释完整
- 遵循团队编码规范
- 完善的异常处理

---

## 📝 验收标准

1. **功能验收**：所有接口功能正常，支持个检和团检场景
2. **性能验收**：接口响应时间 < 3秒，支持并发调用
3. **安全验收**：通过安全测试，无SQL注入等安全漏洞
4. **文档验收**：提供完整的技术文档和用户手册
5. **审计验收**：审计功能完整，支持操作追踪和合规检查

---

## 🚀 项目交付

### 交付物清单
1. 完整的源代码和数据库脚本
2. 部署文档和配置说明  
3. 接口文档和测试用例
4. 用户操作手册
5. 运维监控方案
6. 审计和合规报告模板

### 后续维护
- 提供3个月的免费技术支持
- 定期安全更新和补丁
- 性能优化和功能扩展建议