package com.example.client;

import com.alibaba.fastjson.JSON;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;

import java.util.*;

/**
 * 企业端API客户端示例
 * 
 * <AUTHOR>
 * @date 2024-07-30
 */
public class CompanyApiClient {
    
    private final String baseUrl;
    private final String apiKey;
    private final String apiSecret;
    private final RestTemplate restTemplate;
    
    public CompanyApiClient(String baseUrl, String apiKey, String apiSecret) {
        this.baseUrl = baseUrl;
        this.apiKey = apiKey;
        this.apiSecret = apiSecret;
        this.restTemplate = new RestTemplate();
    }
    
    /**
     * 生成签名
     */
    private String generateSignature(String timestamp, String nonce) {
        String signString = apiKey + apiSecret + timestamp + nonce;
        return DigestUtils.md5Hex(signString).toUpperCase();
    }
    
    /**
     * 创建请求头
     */
    private HttpHeaders createHeaders() {
        String timestamp = String.valueOf(System.currentTimeMillis());
        String nonce = UUID.randomUUID().toString().replace("-", "");
        String signature = generateSignature(timestamp, nonce);
        
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("X-API-Key", apiKey);
        headers.set("X-Timestamp", timestamp);
        headers.set("X-Nonce", nonce);
        headers.set("X-Signature", signature);
        
        return headers;
    }
    
    /**
     * 健康检查
     */
    public ApiResponse<String> healthCheck() {
        String url = baseUrl + "/api/v1/company/health";
        HttpEntity<Void> entity = new HttpEntity<>(createHeaders());
        
        ResponseEntity<String> response = restTemplate.exchange(
            url, HttpMethod.GET, entity, String.class);
        
        return JSON.parseObject(response.getBody(), ApiResponse.class);
    }
    
    /**
     * 创建企业预约
     */
    public ApiResponse<CompanyRegResponse> createCompanyReg(CompanyRegCreateRequest request) {
        String url = baseUrl + "/api/v1/company/registration";
        HttpEntity<CompanyRegCreateRequest> entity = new HttpEntity<>(request, createHeaders());
        
        ResponseEntity<String> response = restTemplate.exchange(
            url, HttpMethod.POST, entity, String.class);
        
        return JSON.parseObject(response.getBody(), ApiResponse.class);
    }
    
    /**
     * 批量创建客户登记
     */
    public ApiResponse<BatchResultResponse> batchCreateCustomers(CustomerBatchCreateRequest request) {
        String url = baseUrl + "/api/v1/company/customers/batch";
        HttpEntity<CustomerBatchCreateRequest> entity = new HttpEntity<>(request, createHeaders());
        
        ResponseEntity<String> response = restTemplate.exchange(
            url, HttpMethod.POST, entity, String.class);
        
        return JSON.parseObject(response.getBody(), ApiResponse.class);
    }
    
    /**
     * 获取企业预约信息
     */
    public ApiResponse<CompanyRegResponse> getCompanyReg(String companyRegId) {
        String url = baseUrl + "/api/v1/company/registration/" + companyRegId;
        HttpEntity<Void> entity = new HttpEntity<>(createHeaders());
        
        ResponseEntity<String> response = restTemplate.exchange(
            url, HttpMethod.GET, entity, String.class);
        
        return JSON.parseObject(response.getBody(), ApiResponse.class);
    }
    
    /**
     * 获取客户登记列表
     */
    public ApiResponse<BatchResultResponse> getCustomerList(String companyRegId, int pageNo, int pageSize) {
        String url = String.format("%s/api/v1/company/registration/%s/customers?pageNo=%d&pageSize=%d",
            baseUrl, companyRegId, pageNo, pageSize);
        HttpEntity<Void> entity = new HttpEntity<>(createHeaders());
        
        ResponseEntity<String> response = restTemplate.exchange(
            url, HttpMethod.GET, entity, String.class);
        
        return JSON.parseObject(response.getBody(), ApiResponse.class);
    }
    
    // DTO类定义
    public static class ApiResponse<T> {
        private Integer code;
        private String message;
        private T data;
        private Long timestamp;
        
        // getters and setters
        public Integer getCode() { return code; }
        public void setCode(Integer code) { this.code = code; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public T getData() { return data; }
        public void setData(T data) { this.data = data; }
        public Long getTimestamp() { return timestamp; }
        public void setTimestamp(Long timestamp) { this.timestamp = timestamp; }
    }
    
    public static class CompanyRegCreateRequest {
        private String companyId;
        private String companyName;
        private String regName;
        private String examType;
        private Date startCheckDate;
        private Date endCheckDate;
        private Integer personCount;
        private String serviceManager;
        private List<TeamCreateRequest> teams;
        
        // getters and setters
        public String getCompanyId() { return companyId; }
        public void setCompanyId(String companyId) { this.companyId = companyId; }
        public String getCompanyName() { return companyName; }
        public void setCompanyName(String companyName) { this.companyName = companyName; }
        public String getRegName() { return regName; }
        public void setRegName(String regName) { this.regName = regName; }
        public String getExamType() { return examType; }
        public void setExamType(String examType) { this.examType = examType; }
        public Date getStartCheckDate() { return startCheckDate; }
        public void setStartCheckDate(Date startCheckDate) { this.startCheckDate = startCheckDate; }
        public Date getEndCheckDate() { return endCheckDate; }
        public void setEndCheckDate(Date endCheckDate) { this.endCheckDate = endCheckDate; }
        public Integer getPersonCount() { return personCount; }
        public void setPersonCount(Integer personCount) { this.personCount = personCount; }
        public String getServiceManager() { return serviceManager; }
        public void setServiceManager(String serviceManager) { this.serviceManager = serviceManager; }
        public List<TeamCreateRequest> getTeams() { return teams; }
        public void setTeams(List<TeamCreateRequest> teams) { this.teams = teams; }
    }
    
    public static class TeamCreateRequest {
        private String teamNum;
        private String name;
        private String examCategory;
        private String post;
        private String sexLimit;
        private Integer minAge;
        private Integer maxAge;
        
        // getters and setters
        public String getTeamNum() { return teamNum; }
        public void setTeamNum(String teamNum) { this.teamNum = teamNum; }
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public String getExamCategory() { return examCategory; }
        public void setExamCategory(String examCategory) { this.examCategory = examCategory; }
        public String getPost() { return post; }
        public void setPost(String post) { this.post = post; }
        public String getSexLimit() { return sexLimit; }
        public void setSexLimit(String sexLimit) { this.sexLimit = sexLimit; }
        public Integer getMinAge() { return minAge; }
        public void setMinAge(Integer minAge) { this.minAge = minAge; }
        public Integer getMaxAge() { return maxAge; }
        public void setMaxAge(Integer maxAge) { this.maxAge = maxAge; }
    }
    
    public static class CustomerBatchCreateRequest {
        private String companyRegId;
        private List<CustomerCreateRequest> customerList;
        
        // getters and setters
        public String getCompanyRegId() { return companyRegId; }
        public void setCompanyRegId(String companyRegId) { this.companyRegId = companyRegId; }
        public List<CustomerCreateRequest> getCustomerList() { return customerList; }
        public void setCustomerList(List<CustomerCreateRequest> customerList) { this.customerList = customerList; }
    }
    
    public static class CustomerCreateRequest {
        private String name;
        private String gender;
        private String idCard;
        private String phone;
        private String teamName;
        private Date appointmentDate;
        
        // getters and setters
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public String getGender() { return gender; }
        public void setGender(String gender) { this.gender = gender; }
        public String getIdCard() { return idCard; }
        public void setIdCard(String idCard) { this.idCard = idCard; }
        public String getPhone() { return phone; }
        public void setPhone(String phone) { this.phone = phone; }
        public String getTeamName() { return teamName; }
        public void setTeamName(String teamName) { this.teamName = teamName; }
        public Date getAppointmentDate() { return appointmentDate; }
        public void setAppointmentDate(Date appointmentDate) { this.appointmentDate = appointmentDate; }
    }
    
    public static class CompanyRegResponse {
        private String id;
        private String companyId;
        private String companyName;
        private String regName;
        private String companyReportNo;
        private Date createTime;
        
        // getters and setters
        public String getId() { return id; }
        public void setId(String id) { this.id = id; }
        public String getCompanyId() { return companyId; }
        public void setCompanyId(String companyId) { this.companyId = companyId; }
        public String getCompanyName() { return companyName; }
        public void setCompanyName(String companyName) { this.companyName = companyName; }
        public String getRegName() { return regName; }
        public void setRegName(String regName) { this.regName = regName; }
        public String getCompanyReportNo() { return companyReportNo; }
        public void setCompanyReportNo(String companyReportNo) { this.companyReportNo = companyReportNo; }
        public Date getCreateTime() { return createTime; }
        public void setCreateTime(Date createTime) { this.createTime = createTime; }
    }
    
    public static class BatchResultResponse {
        private Integer total;
        private Integer successCount;
        private Integer failureCount;
        private List<Object> successList;
        private List<Object> failureList;
        
        // getters and setters
        public Integer getTotal() { return total; }
        public void setTotal(Integer total) { this.total = total; }
        public Integer getSuccessCount() { return successCount; }
        public void setSuccessCount(Integer successCount) { this.successCount = successCount; }
        public Integer getFailureCount() { return failureCount; }
        public void setFailureCount(Integer failureCount) { this.failureCount = failureCount; }
        public List<Object> getSuccessList() { return successList; }
        public void setSuccessList(List<Object> successList) { this.successList = successList; }
        public List<Object> getFailureList() { return failureList; }
        public void setFailureList(List<Object> failureList) { this.failureList = failureList; }
    }
    
    // 使用示例
    public static void main(String[] args) {
        CompanyApiClient client = new CompanyApiClient(
            "http://localhost:8080", 
            "your_api_key", 
            "your_api_secret"
        );
        
        // 健康检查
        ApiResponse<String> healthResponse = client.healthCheck();
        System.out.println("Health check: " + healthResponse.getMessage());
        
        // 创建企业预约
        CompanyRegCreateRequest regRequest = new CompanyRegCreateRequest();
        regRequest.setCompanyId("company123");
        regRequest.setCompanyName("测试企业");
        regRequest.setRegName("2024年度体检");
        regRequest.setExamType("exam_type_001");
        regRequest.setStartCheckDate(new Date());
        regRequest.setEndCheckDate(new Date());
        regRequest.setPersonCount(100);
        
        ApiResponse<CompanyRegResponse> regResponse = client.createCompanyReg(regRequest);
        System.out.println("Company registration created: " + regResponse.getData().getId());
    }
}
