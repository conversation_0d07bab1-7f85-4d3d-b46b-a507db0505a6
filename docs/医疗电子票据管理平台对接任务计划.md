# 医疗电子票据管理平台对接任务计划

## 📋 项目概述

### 项目背景
基于现有体检管理系统与《医疗电子票据管理平台-接口规范vsaas2.0--内蒙古自治区人民医院20250630》文档要求，实现电子票据管理功能的完整对接。

### 核心业务场景
1. **个人体检**：支付完成即时开具电子票据
2. **团检业务**：支持预开票据，后续统一结算的复杂场景
3. **退费处理**：支持全额冲红和部分冲红
4. **统一管理**：提供集中化的票据管理界面
5. **审计合规**：完整的票据审计轨迹和合规性监控

### 技术架构特点
- **支付架构**：支付单(CustomerRegBill) → 多个支付记录(FeePayRecord)
- **团检支持**：后付费标志(afterPayFlag)实现预开票据功能
- **接口追踪**：基于InterfaceTraceLog的完整日志体系
- **状态管理**：支持复杂的票据状态流转和同步
- **审计体系**：全链条票据审计轨迹，支持合规性检查和风险监控

---

## 🎯 任务清单

### 🔥 高优先级任务

#### 1. 分析电子票据接口规范文档，定义API接口契约
**任务编号：** VS-001  
**预计工期：** 3天  
**负责人：** 系统架构师 + 业务分析师

**任务详情：**
- 深度解析vsaas2.0接口规范文档
- 理解票据申请、查询、冲红等核心接口
- 分析团检预开票据的特殊业务场景
- 设计接口安全认证方案（数字签名、Token等）

**交付物：**
- 接口规范解析报告
- API接口设计文档
- 数据传输格式定义
- 错误码映射表
- 接口安全方案

**技术要点：**
```java
// 示例：票据申请接口定义
public interface VsaasReceiptApiService {
    /**
     * 申请电子票据
     * @param request 票据申请请求
     * @return 申请结果
     */
    VsaasReceiptResponse applyReceipt(VsaasReceiptRequest request);
    
    /**
     * 预开电子票据（团检场景）
     * @param request 预开票据请求
     * @return 预开结果
     */
    VsaasReceiptResponse preIssueReceipt(VsaasPreIssueRequest request);
    
    /**
     * 查询票据状态
     * @param receiptNo 票据号
     * @return 票据状态
     */
    VsaasReceiptStatusResponse queryReceiptStatus(String receiptNo);
    
    /**
     * 冲红票据
     * @param request 冲红请求
     * @return 冲红结果
     */
    VsaasRefundResponse refundReceipt(VsaasRefundRequest request);
}
```

#### 2. 创建电子票据管理实体类和数据表结构
**任务编号：** VS-002  
**预计工期：** 2天  
**负责人：** 后端开发工程师

**任务详情：**
- 设计电子票据核心实体类
- 创建数据库表结构和索引
- 建立实体间的关联关系
- 编写数据访问层代码

**数据表结构设计：**

**1. 电子票据主表 (electronic_receipt)**
```sql
CREATE TABLE `electronic_receipt` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `bill_id` varchar(32) NOT NULL COMMENT '关联支付单ID',
  `bill_no` varchar(50) DEFAULT NULL COMMENT '支付单号',
  `receipt_no` varchar(100) DEFAULT NULL COMMENT '电子票据号',
  `vsaas_apply_no` varchar(100) DEFAULT NULL COMMENT 'vsaas申请单号',
  `receipt_type` varchar(20) NOT NULL COMMENT '票据类型：正常开具/预开票据/冲红票据',
  `receipt_status` varchar(20) NOT NULL COMMENT '票据状态：申请中/已开具/已冲红/已作废',
  `total_amount` decimal(10,2) NOT NULL COMMENT '票据总金额',
  `receipt_url` varchar(500) DEFAULT NULL COMMENT '票据URL',
  `qr_code` text COMMENT '电子票据二维码',
  `apply_time` datetime NOT NULL COMMENT '申请时间',
  `issue_time` datetime DEFAULT NULL COMMENT '开具时间',
  `company_reg_id` varchar(32) DEFAULT NULL COMMENT '团检预约ID',
  `after_pay_flag` varchar(1) DEFAULT '0' COMMENT '后付费标志',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` varchar(1) DEFAULT '0' COMMENT '删除标志',
  PRIMARY KEY (`id`),
  KEY `idx_bill_id` (`bill_id`),
  KEY `idx_receipt_no` (`receipt_no`),
  KEY `idx_company_reg_id` (`company_reg_id`),
  KEY `idx_apply_time` (`apply_time`),
  KEY `idx_receipt_status` (`receipt_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='电子票据主表';
```

**2. 票据支付记录关联表 (electronic_receipt_pay_record)**
```sql
CREATE TABLE `electronic_receipt_pay_record` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `receipt_id` varchar(32) NOT NULL COMMENT '关联票据ID',
  `pay_record_id` varchar(32) NOT NULL COMMENT '关联支付记录ID',
  `amount` decimal(10,2) NOT NULL COMMENT '该支付记录对应的票据金额',
  `refund_flag` varchar(1) DEFAULT '0' COMMENT '是否已冲红：0-否，1-是',
  `refund_amount` decimal(10,2) DEFAULT '0.00' COMMENT '冲红金额',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_receipt_id` (`receipt_id`),
  KEY `idx_pay_record_id` (`pay_record_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='票据支付记录关联表';
```

**实体类设计：**
```java
@Data
@TableName("electronic_receipt")
@ApiModel(value = "ElectronicReceipt对象", description = "电子票据")
public class ElectronicReceipt implements Serializable {
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    
    @ApiModelProperty("关联支付单ID")
    private String billId;
    
    @ApiModelProperty("支付单号")
    private String billNo;
    
    @ApiModelProperty("电子票据号")
    private String receiptNo;
    
    @ApiModelProperty("vsaas申请单号")
    private String vsaasApplyNo;
    
    @ApiModelProperty("票据类型")
    @Dict(dicCode = "receipt_type")
    private String receiptType;
    
    @ApiModelProperty("票据状态")
    @Dict(dicCode = "receipt_status")
    private String receiptStatus;
    
    @ApiModelProperty("票据总金额")
    private BigDecimal totalAmount;
    
    @ApiModelProperty("票据URL")
    private String receiptUrl;
    
    @ApiModelProperty("电子票据二维码")
    private String qrCode;
    
    @ApiModelProperty("申请时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date applyTime;
    
    @ApiModelProperty("开具时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date issueTime;
    
    @ApiModelProperty("团检预约ID")
    private String companyRegId;
    
    @ApiModelProperty("后付费标志")
    private String afterPayFlag;
    
    // 关联查询字段
    @TableField(exist = false)
    private CustomerRegBill customerRegBill;
    
    @TableField(exist = false)
    private List<FeePayRecord> feePayRecords;
    
    @TableField(exist = false)
    private CustomerReg customerReg;
}
```

#### 3. 扩展CustomerRegBill实体，增加电子票据相关字段
**任务编号：** VS-003  
**预计工期：** 1天  
**负责人：** 后端开发工程师

**任务详情：**
- 在CustomerRegBill实体中增加电子票据关联字段
- 修改数据库表结构
- 更新相关的Mapper和Service

**数据库字段扩展：**
```sql
ALTER TABLE `customer_reg_bill` 
ADD COLUMN `electronic_receipt_id` varchar(32) DEFAULT NULL COMMENT '关联电子票据ID' AFTER `after_pay_flag`,
ADD COLUMN `electronic_receipt_no` varchar(100) DEFAULT NULL COMMENT '电子票据号' AFTER `electronic_receipt_id`,
ADD COLUMN `electronic_receipt_status` varchar(20) DEFAULT NULL COMMENT '票据状态' AFTER `electronic_receipt_no`,
ADD COLUMN `electronic_receipt_url` varchar(500) DEFAULT NULL COMMENT '票据URL' AFTER `electronic_receipt_status`,
ADD COLUMN `receipt_apply_time` datetime DEFAULT NULL COMMENT '票据申请时间' AFTER `electronic_receipt_url`,
ADD COLUMN `receipt_issue_time` datetime DEFAULT NULL COMMENT '票据开具时间' AFTER `receipt_apply_time`,
ADD COLUMN `pre_issue_flag` varchar(1) DEFAULT '0' COMMENT '预开票据标志' AFTER `receipt_issue_time`;

-- 添加索引
CREATE INDEX `idx_electronic_receipt_id` ON `customer_reg_bill` (`electronic_receipt_id`);
CREATE INDEX `idx_electronic_receipt_no` ON `customer_reg_bill` (`electronic_receipt_no`);
```

**实体类字段扩展：**
```java
// CustomerRegBill.java 新增字段
@ApiModelProperty("关联电子票据ID")
private String electronicReceiptId;

@ApiModelProperty("电子票据号")
private String electronicReceiptNo;

@ApiModelProperty("票据状态")
@Dict(dicCode = "receipt_status")
private String electronicReceiptStatus;

@ApiModelProperty("票据URL")
private String electronicReceiptUrl;

@ApiModelProperty("票据申请时间")
@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
private Date receiptApplyTime;

@ApiModelProperty("票据开具时间")
@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
private Date receiptIssueTime;

@ApiModelProperty("预开票据标志")
private String preIssueFlag;

// 关联查询字段
@TableField(exist = false)
private ElectronicReceipt electronicReceipt;
```

#### 4. 实现支付单级别的电子票据申请接口
**任务编号：** VS-004  
**预计工期：** 4天  
**负责人：** 后端开发工程师

**任务详情：**
- 实现票据申请的核心业务逻辑
- 集成vsaas接口调用
- 处理申请结果和状态更新
- 实现异常处理和重试机制

**核心服务实现：**
```java
@Service
public class ElectronicReceiptServiceImpl implements IElectronicReceiptService {
    
    @Autowired
    private VsaasReceiptApiService vsaasReceiptApiService;
    
    @Autowired
    private ICustomerRegBillService customerRegBillService;
    
    @Autowired
    private IInterfaceTraceLogService interfaceTraceLogService;
    
    /**
     * 申请电子票据（支付单级别）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ElectronicReceipt applyElectronicReceipt(String billId) throws Exception {
        // 1. 验证支付单状态
        CustomerRegBill bill = customerRegBillService.getById(billId);
        if (bill == null) {
            throw new BusinessException("支付单不存在");
        }
        
        // 检查是否已申请票据
        ElectronicReceipt existingReceipt = getByBillId(billId);
        if (existingReceipt != null) {
            throw new BusinessException("该支付单已申请电子票据");
        }
        
        // 验证支付状态
        if (!isAllBillPaid(bill)) {
            throw new BusinessException("支付单未完全支付，无法申请票据");
        }
        
        // 2. 获取支付记录
        List<FeePayRecord> payRecords = feePayRecordService.getByBillId(billId);
        if (payRecords.isEmpty()) {
            throw new BusinessException("未找到相关支付记录");
        }
        
        // 3. 构建票据申请数据
        ElectronicReceipt receipt = new ElectronicReceipt();
        receipt.setBillId(billId);
        receipt.setBillNo(bill.getBillNo());
        receipt.setTotalAmount(bill.getAmount());
        receipt.setReceiptType("正常开具");
        receipt.setReceiptStatus("申请中");
        receipt.setApplyTime(new Date());
        
        // 如果是团检，设置相关信息
        if (StringUtils.isNotBlank(bill.getCompanyRegId())) {
            receipt.setCompanyRegId(bill.getCompanyRegId());
        }
        
        // 4. 调用vsaas接口申请票据
        VsaasReceiptRequest request = buildVsaasRequest(bill, payRecords);
        
        try {
            // 记录接口调用日志
            String reqJson = JSON.toJSONString(request);
            
            VsaasReceiptResponse response = vsaasReceiptApiService.applyReceipt(request);
            
            String respJson = JSON.toJSONString(response);
            
            // 记录接口日志
            logInterfaceTrace(billId, "电子票据申请", reqJson, respJson, true, null);
            
            if (response.isSuccess()) {
                // 申请成功，更新票据信息
                receipt.setVsaasApplyNo(response.getApplyNo());
                receipt.setReceiptStatus("申请中");
                
                // 如果立即返回票据信息，直接更新
                if (StringUtils.isNotBlank(response.getReceiptNo())) {
                    receipt.setReceiptNo(response.getReceiptNo());
                    receipt.setReceiptUrl(response.getReceiptUrl());
                    receipt.setQrCode(response.getQrCode());
                    receipt.setReceiptStatus("已开具");
                    receipt.setIssueTime(new Date());
                }
            } else {
                receipt.setReceiptStatus("申请失败");
                throw new BusinessException("票据申请失败：" + response.getErrorMsg());
            }
            
        } catch (Exception e) {
            // 记录异常日志
            logInterfaceTrace(billId, "电子票据申请", JSON.toJSONString(request), 
                null, false, e.getMessage());
            
            receipt.setReceiptStatus("申请失败");
            throw new BusinessException("票据申请异常：" + e.getMessage());
        }
        
        // 5. 保存票据记录
        save(receipt);
        
        // 6. 更新支付单票据信息
        updateBillReceiptInfo(bill, receipt);
        
        // 7. 创建票据与支付记录的关联关系
        createReceiptPayRecordRelation(receipt.getId(), payRecords);
        
        return receipt;
    }
    
    /**
     * 构建vsaas请求数据
     */
    private VsaasReceiptRequest buildVsaasRequest(CustomerRegBill bill, List<FeePayRecord> payRecords) {
        VsaasReceiptRequest request = new VsaasReceiptRequest();
        
        // 基本信息
        request.setApplyNo(bill.getBillNo());
        request.setTotalAmount(bill.getAmount());
        request.setApplyTime(new Date());
        
        // 患者信息
        CustomerReg customerReg = customerRegService.getById(bill.getCustomerRegId());
        if (customerReg != null) {
            Customer customer = customerService.getById(customerReg.getCustomerId());
            if (customer != null) {
                request.setPatientName(customer.getName());
                request.setPatientIdCard(customer.getIdCard());
                request.setPatientPhone(customer.getPhone());
            }
        }
        
        // 费用明细
        List<VsaasReceiptItem> items = new ArrayList<>();
        for (FeePayRecord payRecord : payRecords) {
            VsaasReceiptItem item = new VsaasReceiptItem();
            item.setItemName(payRecord.getBizName());
            item.setAmount(payRecord.getAmount());
            item.setPayChannel(payRecord.getPayChannel());
            items.add(item);
        }
        request.setItems(items);
        
        return request;
    }
    
    /**
     * 更新支付单票据信息
     */
    private void updateBillReceiptInfo(CustomerRegBill bill, ElectronicReceipt receipt) {
        bill.setElectronicReceiptId(receipt.getId());
        bill.setElectronicReceiptNo(receipt.getReceiptNo());
        bill.setElectronicReceiptStatus(receipt.getReceiptStatus());
        bill.setElectronicReceiptUrl(receipt.getReceiptUrl());
        bill.setReceiptApplyTime(receipt.getApplyTime());
        bill.setReceiptIssueTime(receipt.getIssueTime());
        
        customerRegBillService.updateById(bill);
    }
}
```

#### 5. 实现团检预开票据功能(后付费场景)
**任务编号：** VS-005  
**预计工期：** 3天  
**负责人：** 后端开发工程师

**任务详情：**
- 实现团检预开票据的特殊逻辑
- 处理后付费标志的业务场景
- 实现预开票据与后续支付的关联
- 处理团检分组级别的票据管理

**团检预开票据服务：**
```java
@Service
public class CompanyReceiptServiceImpl implements ICompanyReceiptService {
    
    /**
     * 团检预开票据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ElectronicReceipt preIssueReceiptForCompany(String companyRegId) throws Exception {
        // 1. 获取团检预约信息
        CompanyReg companyReg = companyRegService.getById(companyRegId);
        if (companyReg == null) {
            throw new BusinessException("团检预约不存在");
        }
        
        // 2. 计算团检总金额
        BigDecimal totalAmount = calculateCompanyRegAmount(companyRegId);
        if (totalAmount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException("团检金额为0，无法预开票据");
        }
        
        // 3. 检查是否已预开票据
        ElectronicReceipt existingReceipt = getByCompanyRegId(companyRegId);
        if (existingReceipt != null) {
            throw new BusinessException("该团检已预开票据");
        }
        
        // 4. 创建预开票据记录
        ElectronicReceipt receipt = new ElectronicReceipt();
        receipt.setCompanyRegId(companyRegId);
        receipt.setTotalAmount(totalAmount);
        receipt.setReceiptType("预开票据");
        receipt.setReceiptStatus("申请中");
        receipt.setAfterPayFlag("1");
        receipt.setApplyTime(new Date());
        
        // 5. 构建预开票据请求
        VsaasPreIssueRequest request = buildPreIssueRequest(companyReg, totalAmount);
        
        try {
            // 调用vsaas预开票据接口
            VsaasReceiptResponse response = vsaasReceiptApiService.preIssueReceipt(request);
            
            if (response.isSuccess()) {
                receipt.setVsaasApplyNo(response.getApplyNo());
                receipt.setReceiptNo(response.getReceiptNo());
                receipt.setReceiptUrl(response.getReceiptUrl());
                receipt.setQrCode(response.getQrCode());
                receipt.setReceiptStatus("已开具");
                receipt.setIssueTime(new Date());
            } else {
                throw new BusinessException("预开票据失败：" + response.getErrorMsg());
            }
            
        } catch (Exception e) {
            receipt.setReceiptStatus("申请失败");
            throw new BusinessException("预开票据异常：" + e.getMessage());
        }
        
        // 6. 保存票据记录
        save(receipt);
        
        // 7. 更新团检相关支付单的预开票据标志
        updateCompanyBillsPreIssueFlag(companyRegId, receipt);
        
        // 8. 创建0元先检后付的支付记录（用于关联预开票据）
        createPreIssuePayRecord(companyRegId, receipt);
        
        return receipt;
    }
    
    /**
     * 团检实际支付时关联预开票据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void linkPreIssueReceiptWithPayment(String billId) throws Exception {
        CustomerRegBill bill = customerRegBillService.getById(billId);
        if (bill == null || StringUtils.isBlank(bill.getCompanyRegId())) {
            return;
        }
        
        // 查找该团检的预开票据
        ElectronicReceipt preIssueReceipt = getByCompanyRegId(bill.getCompanyRegId());
        if (preIssueReceipt == null || !"预开票据".equals(preIssueReceipt.getReceiptType())) {
            return;
        }
        
        // 获取实际支付记录
        List<FeePayRecord> payRecords = feePayRecordService.getByBillId(billId);
        
        // 创建预开票据与实际支付的关联关系
        for (FeePayRecord payRecord : payRecords) {
            if (payRecord.getAmount().compareTo(BigDecimal.ZERO) > 0) {
                ElectronicReceiptPayRecord relation = new ElectronicReceiptPayRecord();
                relation.setReceiptId(preIssueReceipt.getId());
                relation.setPayRecordId(payRecord.getId());
                relation.setAmount(payRecord.getAmount());
                electronicReceiptPayRecordService.save(relation);
            }
        }
        
        // 更新支付单的票据关联信息
        bill.setElectronicReceiptId(preIssueReceipt.getId());
        bill.setElectronicReceiptNo(preIssueReceipt.getReceiptNo());
        bill.setElectronicReceiptStatus(preIssueReceipt.getReceiptStatus());
        bill.setElectronicReceiptUrl(preIssueReceipt.getReceiptUrl());
        bill.setPreIssueFlag("1");
        
        customerRegBillService.updateById(bill);
    }
    
    /**
     * 计算团检总金额
     */
    private BigDecimal calculateCompanyRegAmount(String companyRegId) {
        // 基于现有逻辑计算团检各分组的总金额
        String sql = "SELECT SUM(cg.price_after_dis) FROM customer_reg c " +
                    "JOIN customer_reg_item_group cg ON c.id = cg.customer_reg_id " +
                    "WHERE cg.give_away_flag = '0' AND cg.payer_type = '单位支付' " +
                    "AND cg.add_minus_flag != '-1' AND c.company_reg_id = ?";
        
        return customerRegBillMapper.selectSum(sql, companyRegId);
    }
}
```

#### 6. 创建统一电子发票管理页面和控制器
**任务编号：** VS-006  
**预计工期：** 5天  
**负责人：** 全栈开发工程师

#### 7. 设计和实现电子票据审计体系
**任务编号：** VS-007  
**预计工期：** 4天  
**负责人：** 系统架构师 + 后端开发工程师

**任务详情：**
- 创建统一的电子发票管理后端接口
- 实现前端管理页面和交互逻辑
- 提供多维度查询和筛选功能
- 实现票据状态的实时展示

**后端控制器实现：**
```java
@RestController
@RequestMapping("/invoice/electronicReceipt")
@Api(tags = "统一电子发票管理")
public class ElectronicReceiptController {
    
    @Autowired
    private IElectronicReceiptService electronicReceiptService;
    
    /**
     * 统一发票列表查询
     */
    @GetMapping("/list")
    @ApiOperation("统一发票列表查询")
    public Result<IPage<ElectronicReceiptVO>> list(
        @RequestParam Map<String, Object> params,
        ElectronicReceiptQueryDTO query) {
        
        Page<ElectronicReceiptVO> page = electronicReceiptService.getUnifiedList(
            new Page<>(query.getPageNo(), query.getPageSize()), query);
        return Result.OK(page);
    }
    
    /**
     * 获取发票统计数据
     */
    @GetMapping("/statistics")
    @ApiOperation("发票统计数据")
    public Result<ReceiptStatisticsVO> getStatistics(
        @RequestParam(required = false) String startDate,
        @RequestParam(required = false) String endDate) {
        
        ReceiptStatisticsVO statistics = electronicReceiptService.getStatistics(startDate, endDate);
        return Result.OK(statistics);
    }
    
    /**
     * 申请电子票据
     */
    @PostMapping("/apply")
    @ApiOperation("申请电子票据")
    public Result<String> applyReceipt(@RequestParam String billId) {
        try {
            ElectronicReceipt receipt = electronicReceiptService.applyElectronicReceipt(billId);
            return Result.OK("票据申请成功", receipt.getId());
        } catch (Exception e) {
            return Result.error("票据申请失败：" + e.getMessage());
        }
    }
    
    /**
     * 批量申请电子票据
     */
    @PostMapping("/batchApply")
    @ApiOperation("批量申请电子票据")
    public Result<BatchOperationResult> batchApply(@RequestBody List<String> billIds) {
        BatchOperationResult result = electronicReceiptService.batchApplyReceipts(billIds);
        return Result.OK(result);
    }
    
    /**
     * 冲红票据
     */
    @PostMapping("/refund")
    @ApiOperation("冲红票据")
    public Result<String> refundReceipt(@RequestBody RefundReceiptDTO refundDTO) {
        try {
            electronicReceiptService.refundReceipt(refundDTO);
            return Result.OK("票据冲红成功");
        } catch (Exception e) {
            return Result.error("票据冲红失败：" + e.getMessage());
        }
    }
    
    /**
     * 批量冲红票据
     */
    @PostMapping("/batchRefund")
    @ApiOperation("批量冲红票据")
    public Result<BatchOperationResult> batchRefund(@RequestBody BatchRefundDTO refundDTO) {
        BatchOperationResult result = electronicReceiptService.batchRefundReceipts(refundDTO);
        return Result.OK(result);
    }
    
    /**
     * 下载票据
     */
    @GetMapping("/download")
    @ApiOperation("下载票据")
    public void downloadReceipt(@RequestParam String receiptId, HttpServletResponse response) {
        electronicReceiptService.downloadReceipt(receiptId, response);
    }
    
    /**
     * 批量下载票据
     */
    @PostMapping("/batchDownload")
    @ApiOperation("批量下载票据")
    public Result<String> batchDownload(@RequestBody List<String> receiptIds) {
        String zipFileUrl = electronicReceiptService.batchDownloadReceipts(receiptIds);
        return Result.OK("批量下载任务已创建", zipFileUrl);
    }
    
    /**
     * 同步票据状态
     */
    @PostMapping("/syncStatus")
    @ApiOperation("同步票据状态")
    public Result<String> syncReceiptStatus(@RequestBody List<String> receiptIds) {
        electronicReceiptService.syncReceiptStatus(receiptIds);
        return Result.OK("同步任务已提交");
    }
    
    /**
     * 获取票据详情
     */
    @GetMapping("/detail")
    @ApiOperation("获取票据详情")
    public Result<ElectronicReceiptDetailVO> getReceiptDetail(@RequestParam String receiptId) {
        ElectronicReceiptDetailVO detail = electronicReceiptService.getReceiptDetail(receiptId);
        return Result.OK(detail);
    }
    
    /**
     * 重新申请票据
     */
    @PostMapping("/retry")
    @ApiOperation("重新申请票据")
    public Result<String> retryApplyReceipt(@RequestParam String receiptId) {
        try {
            electronicReceiptService.retryApplyReceipt(receiptId);
            return Result.OK("重新申请成功");
        } catch (Exception e) {
            return Result.error("重新申请失败：" + e.getMessage());
        }
    }
}
```

**前端页面路由配置：**
```javascript
// router/routes/modules/invoice.js
export default {
  path: '/invoice',
  name: 'Invoice',
  redirect: '/invoice/electronic-receipt',
  component: LAYOUT,
  meta: {
    title: '发票管理',
    icon: 'receipt',
  },
  children: [
    {
      path: 'electronic-receipt',
      name: 'ElectronicReceiptManagement',
      component: () => import('/@/views/invoice/ElectronicReceiptManagement.vue'),
      meta: {
        title: '电子票据管理',
        icon: 'file-text',
      },
    },
    {
      path: 'statistics',
      name: 'ReceiptStatistics',
      component: () => import('/@/views/invoice/ReceiptStatistics.vue'),
      meta: {
        title: '票据统计',
        icon: 'bar-chart',
      },
    },
  ],
};
```

**任务详情：**
- 设计完整的票据审计数据模型
- 实现操作轨迹记录和审计日志
- 建立合规性检查和风险监控机制
- 提供审计查询和报告功能

**审计数据表设计：**

**1. 票据审计日志表 (electronic_receipt_audit_log)**
```sql
CREATE TABLE `electronic_receipt_audit_log` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `receipt_id` varchar(32) DEFAULT NULL COMMENT '关联票据ID',
  `bill_id` varchar(32) DEFAULT NULL COMMENT '关联支付单ID',
  `audit_type` varchar(30) NOT NULL COMMENT '审计类型：CREATE/UPDATE/DELETE/APPLY/REFUND/DOWNLOAD',
  `operation_desc` varchar(200) NOT NULL COMMENT '操作描述',
  `operator_id` varchar(32) NOT NULL COMMENT '操作人ID',
  `operator_name` varchar(50) NOT NULL COMMENT '操作人姓名',
  `operator_role` varchar(50) DEFAULT NULL COMMENT '操作人角色',
  `client_ip` varchar(50) DEFAULT NULL COMMENT '客户端IP',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `before_data` longtext COMMENT '操作前数据快照',
  `after_data` longtext COMMENT '操作后数据快照',
  `business_context` longtext COMMENT '业务上下文信息',
  `risk_level` varchar(10) DEFAULT 'LOW' COMMENT '风险级别：LOW/MEDIUM/HIGH/CRITICAL',
  `compliance_flag` varchar(1) DEFAULT '1' COMMENT '合规标志：1-合规，0-不合规',
  `audit_result` varchar(20) DEFAULT 'NORMAL' COMMENT '审计结果：NORMAL/WARNING/ERROR/BLOCKED',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_receipt_id` (`receipt_id`),
  KEY `idx_bill_id` (`bill_id`),
  KEY `idx_operator_id` (`operator_id`),
  KEY `idx_audit_type` (`audit_type`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_risk_level` (`risk_level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='票据审计日志表';
```

**2. 票据合规检查记录表 (electronic_receipt_compliance_check)**
```sql
CREATE TABLE `electronic_receipt_compliance_check` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `receipt_id` varchar(32) NOT NULL COMMENT '关联票据ID',
  `check_type` varchar(30) NOT NULL COMMENT '检查类型：AMOUNT/TIME/STATUS/PROCESS',
  `check_rule` varchar(100) NOT NULL COMMENT '检查规则',
  `check_result` varchar(20) NOT NULL COMMENT '检查结果：PASS/FAIL/WARNING',
  `check_detail` text COMMENT '检查详情',
  `risk_score` int DEFAULT '0' COMMENT '风险评分',
  `auto_fix_flag` varchar(1) DEFAULT '0' COMMENT '是否自动修复',
  `fix_action` varchar(200) DEFAULT NULL COMMENT '修复动作',
  `check_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '检查时间',
  `checker` varchar(50) DEFAULT 'SYSTEM' COMMENT '检查者',
  PRIMARY KEY (`id`),
  KEY `idx_receipt_id` (`receipt_id`),
  KEY `idx_check_type` (`check_type`),
  KEY `idx_check_result` (`check_result`),
  KEY `idx_check_time` (`check_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='票据合规检查记录表';
```

**审计核心服务实现：**
```java
@Service
public class ElectronicReceiptAuditService {
    
    @Autowired
    private ElectronicReceiptAuditLogMapper auditLogMapper;
    
    @Autowired
    private ElectronicReceiptComplianceCheckMapper complianceCheckMapper;
    
    /**
     * 记录票据操作审计日志
     */
    public void recordAuditLog(String receiptId, String billId, String auditType, 
                              String operationDesc, Object beforeData, Object afterData) {
        // 获取当前操作人信息
        LoginUser currentUser = SecurityUtils.getLoginUser();
        HttpServletRequest request = RequestHolder.getHttpServletRequest();
        
        ElectronicReceiptAuditLog auditLog = new ElectronicReceiptAuditLog();
        auditLog.setReceiptId(receiptId);
        auditLog.setBillId(billId);
        auditLog.setAuditType(auditType);
        auditLog.setOperationDesc(operationDesc);
        auditLog.setOperatorId(currentUser.getId());
        auditLog.setOperatorName(currentUser.getRealname());
        auditLog.setOperatorRole(currentUser.getCurrentRole());
        auditLog.setClientIp(IPUtils.getIpAddr(request));
        auditLog.setUserAgent(request.getHeader("User-Agent"));
        
        // 记录数据快照
        if (beforeData != null) {
            auditLog.setBeforeData(JSON.toJSONString(beforeData));
        }
        if (afterData != null) {
            auditLog.setAfterData(JSON.toJSONString(afterData));
        }
        
        // 构建业务上下文信息
        Map<String, Object> businessContext = buildBusinessContext(receiptId, billId);
        auditLog.setBusinessContext(JSON.toJSONString(businessContext));
        
        // 风险评估
        String riskLevel = assessRiskLevel(auditType, beforeData, afterData);
        auditLog.setRiskLevel(riskLevel);
        
        // 合规性检查
        boolean isCompliant = checkCompliance(auditType, beforeData, afterData);
        auditLog.setComplianceFlag(isCompliant ? "1" : "0");
        
        // 审计结果判定
        String auditResult = determineAuditResult(riskLevel, isCompliant);
        auditLog.setAuditResult(auditResult);
        
        auditLogMapper.insert(auditLog);
        
        // 高风险操作触发告警
        if ("HIGH".equals(riskLevel) || "CRITICAL".equals(riskLevel)) {
            triggerRiskAlert(auditLog);
        }
    }
    
    /**
     * 执行合规性检查
     */
    public void performComplianceCheck(String receiptId) {
        ElectronicReceipt receipt = electronicReceiptService.getById(receiptId);
        if (receipt == null) {
            return;
        }
        
        // 执行各类合规检查
        checkAmountCompliance(receipt);
        checkTimeCompliance(receipt);
        checkStatusCompliance(receipt);
        checkProcessCompliance(receipt);
    }
    
    /**
     * 金额合规检查
     */
    private void checkAmountCompliance(ElectronicReceipt receipt) {
        ElectronicReceiptComplianceCheck check = new ElectronicReceiptComplianceCheck();
        check.setReceiptId(receipt.getId());
        check.setCheckType("AMOUNT");
        check.setCheckRule("票据金额与支付金额一致性检查");
        
        // 获取关联支付单
        CustomerRegBill bill = customerRegBillService.getById(receipt.getBillId());
        if (bill != null) {
            BigDecimal receiptAmount = receipt.getTotalAmount();
            BigDecimal billAmount = bill.getAmount();
            
            if (receiptAmount.compareTo(billAmount) == 0) {
                check.setCheckResult("PASS");
                check.setCheckDetail("票据金额与支付金额一致");
                check.setRiskScore(0);
            } else {
                check.setCheckResult("FAIL");
                check.setCheckDetail(String.format("票据金额(%s)与支付金额(%s)不一致", 
                    receiptAmount, billAmount));
                check.setRiskScore(80);
            }
        } else {
            check.setCheckResult("WARNING");
            check.setCheckDetail("未找到关联支付单");
            check.setRiskScore(30);
        }
        
        complianceCheckMapper.insert(check);
    }
    
    /**
     * 时间合规检查
     */
    private void checkTimeCompliance(ElectronicReceipt receipt) {
        ElectronicReceiptComplianceCheck check = new ElectronicReceiptComplianceCheck();
        check.setReceiptId(receipt.getId());
        check.setCheckType("TIME");
        check.setCheckRule("票据开具时间合理性检查");
        
        Date applyTime = receipt.getApplyTime();
        Date issueTime = receipt.getIssueTime();
        
        if (applyTime != null && issueTime != null) {
            long timeDiff = issueTime.getTime() - applyTime.getTime();
            long hours = timeDiff / (1000 * 60 * 60);
            
            if (hours <= 24) {
                check.setCheckResult("PASS");
                check.setCheckDetail("票据开具时间正常");
                check.setRiskScore(0);
            } else if (hours <= 72) {
                check.setCheckResult("WARNING");
                check.setCheckDetail(String.format("票据开具耗时%d小时，超过正常范围", hours));
                check.setRiskScore(30);
            } else {
                check.setCheckResult("FAIL");
                check.setCheckDetail(String.format("票据开具耗时%d小时，严重超时", hours));
                check.setRiskScore(70);
            }
        } else {
            check.setCheckResult("WARNING");
            check.setCheckDetail("票据时间信息不完整");
            check.setRiskScore(20);
        }
        
        complianceCheckMapper.insert(check);
    }
    
    /**
     * 审计查询服务
     */
    public IPage<ElectronicReceiptAuditLogVO> queryAuditLogs(Page<ElectronicReceiptAuditLogVO> page, 
                                                           AuditLogQueryDTO query) {
        return auditLogMapper.selectAuditLogPage(page, query);
    }
    
    /**
     * 生成审计报告
     */
    public AuditReportVO generateAuditReport(String startDate, String endDate) {
        AuditReportVO report = new AuditReportVO();
        
        // 操作统计
        report.setOperationStats(getOperationStatistics(startDate, endDate));
        
        // 风险统计
        report.setRiskStats(getRiskStatistics(startDate, endDate));
        
        // 合规统计
        report.setComplianceStats(getComplianceStatistics(startDate, endDate));
        
        // 异常操作
        report.setAbnormalOperations(getAbnormalOperations(startDate, endDate));
        
        // 用户操作分析
        report.setUserOperationAnalysis(getUserOperationAnalysis(startDate, endDate));
        
        return report;
    }
    
    /**
     * 实时风险监控
     */
    @EventListener
    public void handleReceiptOperationEvent(ReceiptOperationEvent event) {
        // 实时风险评估
        RiskAssessmentResult risk = assessOperationRisk(event);
        
        if (risk.getRiskLevel() >= RiskLevel.HIGH) {
            // 触发告警
            alertService.sendRiskAlert(risk);
            
            // 如果是关键风险，自动阻断操作
            if (risk.getRiskLevel() == RiskLevel.CRITICAL) {
                blockService.blockOperation(event.getOperationId());
            }
        }
        
        // 记录风险评估结果
        recordRiskAssessment(event, risk);
    }
}
```

**审计前端界面设计：**
```vue
<!-- ElectronicReceiptAudit.vue -->
<template>
  <div class="receipt-audit-management">
    <a-card title="电子票据审计管理">
      
      <!-- 审计概览 -->
      <div class="audit-overview">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-statistic 
              title="今日审计事件" 
              :value="auditStats.todayEvents" 
              :value-style="{ color: '#1890ff' }" />
          </a-col>
          <a-col :span="6">
            <a-statistic 
              title="高风险操作" 
              :value="auditStats.highRiskOps" 
              :value-style="{ color: '#f5222d' }" />
          </a-col>
          <a-col :span="6">
            <a-statistic 
              title="合规率" 
              :value="auditStats.complianceRate" 
              suffix="%" 
              :value-style="{ color: '#52c41a' }" />
          </a-col>
          <a-col :span="6">
            <a-statistic 
              title="待处理告警" 
              :value="auditStats.pendingAlerts" 
              :value-style="{ color: '#fa8c16' }" />
          </a-col>
        </a-row>
      </div>
      
      <!-- 审计日志查询 -->
      <div class="audit-log-section">
        <a-form layout="inline" :model="auditQuery">
          <a-form-item label="审计类型">
            <a-select v-model:value="auditQuery.auditType" style="width: 150px">
              <a-select-option value="">全部</a-select-option>
              <a-select-option value="CREATE">创建</a-select-option>
              <a-select-option value="UPDATE">更新</a-select-option>
              <a-select-option value="APPLY">申请</a-select-option>
              <a-select-option value="REFUND">冲红</a-select-option>
              <a-select-option value="DOWNLOAD">下载</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="风险级别">
            <a-select v-model:value="auditQuery.riskLevel" style="width: 120px">
              <a-select-option value="">全部</a-select-option>
              <a-select-option value="LOW">低风险</a-select-option>
              <a-select-option value="MEDIUM">中风险</a-select-option>
              <a-select-option value="HIGH">高风险</a-select-option>
              <a-select-option value="CRITICAL">关键风险</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="操作时间">
            <a-range-picker v-model:value="auditQuery.timeRange" />
          </a-form-item>
          
          <a-form-item label="操作人">
            <a-input v-model:value="auditQuery.operatorName" placeholder="操作人姓名" />
          </a-form-item>
          
          <a-form-item>
            <a-button type="primary" @click="searchAuditLogs">
              <template #icon><SearchOutlined /></template>
              查询
            </a-button>
            <a-button @click="exportAuditReport" style="margin-left: 8px">
              <template #icon><ExportOutlined /></template>
              导出报告
            </a-button>
          </a-form-item>
        </a-form>
        
        <!-- 审计日志表格 -->
        <a-table
          :columns="auditLogColumns"
          :data-source="auditLogData"
          :loading="loading"
          :pagination="pagination"
          row-key="id"
          @change="handleTableChange">
          
          <template #auditType="{ record }">
            <a-tag :color="getAuditTypeColor(record.auditType)">
              {{ getAuditTypeText(record.auditType) }}
            </a-tag>
          </template>
          
          <template #riskLevel="{ record }">
            <a-tag :color="getRiskLevelColor(record.riskLevel)">
              {{ getRiskLevelText(record.riskLevel) }}
            </a-tag>
          </template>
          
          <template #complianceFlag="{ record }">
            <a-tag :color="record.complianceFlag === '1' ? 'green' : 'red'">
              {{ record.complianceFlag === '1' ? '合规' : '不合规' }}
            </a-tag>
          </template>
          
          <template #action="{ record }">
            <a-space>
              <a-button type="link" size="small" @click="viewAuditDetail(record)">
                查看详情
              </a-button>
              <a-button type="link" size="small" @click="viewDataDiff(record)" 
                        v-if="record.beforeData || record.afterData">
                数据对比
              </a-button>
            </a-space>
          </template>
        </a-table>
      </div>
      
      <!-- 合规检查结果 -->
      <div class="compliance-check-section">
        <a-card title="合规检查结果" size="small">
          <a-table
            :columns="complianceColumns"
            :data-source="complianceData"
            :pagination="false"
            size="small">
            
            <template #checkResult="{ record }">
              <a-tag :color="getComplianceResultColor(record.checkResult)">
                {{ getComplianceResultText(record.checkResult) }}
              </a-tag>
            </template>
            
            <template #riskScore="{ record }">
              <a-progress 
                :percent="record.riskScore" 
                :status="record.riskScore > 70 ? 'exception' : record.riskScore > 30 ? 'active' : 'success'"
                :show-info="false" 
                size="small" />
              <span style="margin-left: 8px">{{ record.riskScore }}</span>
            </template>
          </a-table>
        </a-card>
      </div>
    </a-card>
    
    <!-- 审计详情抽屉 -->
    <AuditDetailDrawer
      v-model:visible="auditDetailVisible"
      :audit-log="currentAuditLog"
    />
    
    <!-- 数据对比抽屉 -->
    <DataDiffDrawer
      v-model:visible="dataDiffVisible"
      :before-data="beforeData"
      :after-data="afterData"
    />
  </div>
</template>
```

### 🔸 中优先级任务

#### 7. 完善InterfaceTraceLog，增加票据接口追踪
**任务编号：** VS-007  
**预计工期：** 2天  
**负责人：** 后端开发工程师

**任务详情：**
- 扩展InterfaceTraceLog支持票据接口类型
- 实现票据接口调用的统一日志记录
- 提供接口调用统计和分析功能

**扩展接口类型常量：**
```java
// ExConstants.java 新增票据接口类型
public static final String INTERFACE_TYPE_电子票据申请 = "电子票据申请";
public static final String INTERFACE_TYPE_电子票据查询 = "电子票据查询";
public static final String INTERFACE_TYPE_电子票据冲红 = "电子票据冲红";
public static final String INTERFACE_TYPE_预开票据 = "预开票据";
public static final String INTERFACE_TYPE_票据状态同步 = "票据状态同步";
```

**票据接口日志服务：**
```java
@Service
public class ReceiptInterfaceLogService {
    
    @Autowired
    private IInterfaceTraceLogService interfaceTraceLogService;
    
    /**
     * 记录票据接口调用日志
     */
    public void logReceiptInterface(String businessId, String interfaceType, 
                                  String reqBody, String respBody, 
                                  boolean success, String errorMsg) {
        InterfaceTraceLog log = new InterfaceTraceLog();
        log.setBusinessId(businessId);
        log.setType(interfaceType);
        log.setReqBody(reqBody);
        log.setRespBody(respBody);
        log.setOkFlag(success ? "1" : "0");
        log.setErrorMsg(errorMsg);
        log.setCostTime(System.currentTimeMillis() - startTime);
        log.setCreateTime(new Date());
        
        interfaceTraceLogService.save(log);
    }
    
    /**
     * 获取票据接口调用统计
     */
    public ReceiptInterfaceStatVO getReceiptInterfaceStats(String startDate, String endDate) {
        // 统计各类票据接口的调用次数和成功率
        return interfaceTraceLogService.getReceiptInterfaceStats(startDate, endDate);
    }
}
```

#### 8. 实现电子票据状态查询和同步机制
**任务编号：** VS-008  
**预计工期：** 3天  
**负责人：** 后端开发工程师

**任务详情：**
- 实现定时查询vsaas票据状态的机制
- 更新本地票据状态和相关信息
- 处理状态同步异常和重试逻辑

**状态同步服务：**
```java
@Service
public class ReceiptStatusSyncService {
    
    @Autowired
    private VsaasReceiptApiService vsaasReceiptApiService;
    
    /**
     * 定时同步票据状态
     */
    @Scheduled(cron = "0 */10 * * * ?") // 每10分钟执行一次
    public void syncReceiptStatusScheduled() {
        // 查询状态为"申请中"的票据
        List<ElectronicReceipt> pendingReceipts = electronicReceiptService.getPendingReceipts();
        
        for (ElectronicReceipt receipt : pendingReceipts) {
            try {
                syncSingleReceiptStatus(receipt);
                Thread.sleep(1000); // 避免频繁调用
            } catch (Exception e) {
                log.error("同步票据状态失败，receiptId: {}", receipt.getId(), e);
            }
        }
    }
    
    /**
     * 同步单个票据状态
     */
    public void syncSingleReceiptStatus(ElectronicReceipt receipt) throws Exception {
        String receiptNo = receipt.getReceiptNo();
        if (StringUtils.isBlank(receiptNo)) {
            receiptNo = receipt.getVsaasApplyNo();
        }
        
        if (StringUtils.isBlank(receiptNo)) {
            return;
        }
        
        // 调用vsaas查询接口
        VsaasReceiptStatusResponse response = vsaasReceiptApiService.queryReceiptStatus(receiptNo);
        
        if (response.isSuccess()) {
            // 更新票据状态
            boolean updated = false;
            
            if (!receipt.getReceiptStatus().equals(response.getStatus())) {
                receipt.setReceiptStatus(response.getStatus());
                updated = true;
            }
            
            if (StringUtils.isNotBlank(response.getReceiptUrl()) && 
                !response.getReceiptUrl().equals(receipt.getReceiptUrl())) {
                receipt.setReceiptUrl(response.getReceiptUrl());
                updated = true;
            }
            
            if (StringUtils.isNotBlank(response.getQrCode()) && 
                !response.getQrCode().equals(receipt.getQrCode())) {
                receipt.setQrCode(response.getQrCode());
                updated = true;
            }
            
            if ("已开具".equals(response.getStatus()) && receipt.getIssueTime() == null) {
                receipt.setIssueTime(response.getIssueTime());
                updated = true;
            }
            
            if (updated) {
                electronicReceiptService.updateById(receipt);
                
                // 同步更新支付单信息
                updateBillReceiptStatus(receipt);
            }
        }
        
        // 记录接口调用日志
        receiptInterfaceLogService.logReceiptInterface(
            receipt.getId(), "票据状态同步", receiptNo, 
            JSON.toJSONString(response), response.isSuccess(), response.getErrorMsg());
    }
}
```

#### 9. 实现电子票据冲红/作废接口(支持部分冲红)
**任务编号：** VS-009  
**预计工期：** 4天  
**负责人：** 后端开发工程师

**任务详情：**
- 实现全额冲红和部分冲红功能
- 处理退费流程中的票据冲红逻辑
- 支持团检预开票据的冲红处理

**票据冲红服务：**
```java
@Service
public class ReceiptRefundServiceImpl implements IReceiptRefundService {
    
    /**
     * 全额冲红票据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void refundReceipt(String receiptId, String refundReason) throws Exception {
        ElectronicReceipt receipt = electronicReceiptService.getById(receiptId);
        if (receipt == null) {
            throw new BusinessException("票据不存在");
        }
        
        if (!"已开具".equals(receipt.getReceiptStatus())) {
            throw new BusinessException("只有已开具的票据才能冲红");
        }
        
        // 调用vsaas冲红接口
        VsaasRefundRequest request = new VsaasRefundRequest();
        request.setOriginalReceiptNo(receipt.getReceiptNo());
        request.setRefundAmount(receipt.getTotalAmount());
        request.setRefundReason(refundReason);
        request.setRefundType("全额冲红");
        
        VsaasRefundResponse response = vsaasReceiptApiService.refundReceipt(request);
        
        if (response.isSuccess()) {
            // 更新原票据状态
            receipt.setReceiptStatus("已冲红");
            electronicReceiptService.updateById(receipt);
            
            // 创建冲红票据记录
            createRefundReceiptRecord(receipt, response, receipt.getTotalAmount());
            
            // 更新支付单状态
            updateBillRefundStatus(receipt.getBillId());
            
        } else {
            throw new BusinessException("票据冲红失败：" + response.getErrorMsg());
        }
    }
    
    /**
     * 部分冲红票据（基于支付记录）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void partialRefundReceiptByPayRecord(String payRecordId, BigDecimal refundAmount, 
                                              String refundReason) throws Exception {
        FeePayRecord payRecord = feePayRecordService.getById(payRecordId);
        if (payRecord == null) {
            throw new BusinessException("支付记录不存在");
        }
        
        // 查找关联的票据
        ElectronicReceiptPayRecord relation = electronicReceiptPayRecordService
            .getByPayRecordId(payRecordId);
        if (relation == null) {
            throw new BusinessException("未找到关联的票据记录");
        }
        
        ElectronicReceipt receipt = electronicReceiptService.getById(relation.getReceiptId());
        if (!"已开具".equals(receipt.getReceiptStatus())) {
            throw new BusinessException("只有已开具的票据才能冲红");
        }
        
        // 验证冲红金额
        if (refundAmount.compareTo(relation.getAmount()) > 0) {
            throw new BusinessException("冲红金额不能超过该支付记录对应的票据金额");
        }
        
        // 调用vsaas部分冲红接口
        VsaasPartialRefundRequest request = new VsaasPartialRefundRequest();
        request.setOriginalReceiptNo(receipt.getReceiptNo());
        request.setRefundAmount(refundAmount);
        request.setRefundReason(refundReason);
        request.setRefundType("部分冲红");
        request.setPayRecordId(payRecordId);
        
        VsaasRefundResponse response = vsaasReceiptApiService.partialRefundReceipt(request);
        
        if (response.isSuccess()) {
            // 更新关联记录的冲红标志
            relation.setRefundFlag("1");
            relation.setRefundAmount(refundAmount);
            electronicReceiptPayRecordService.updateById(relation);
            
            // 创建部分冲红票据记录
            createPartialRefundReceiptRecord(receipt, response, refundAmount, payRecordId);
            
            // 检查是否全部冲红完成
            checkAndUpdateReceiptRefundStatus(receipt.getId());
            
        } else {
            throw new BusinessException("部分冲红失败：" + response.getErrorMsg());
        }
    }
    
    /**
     * 批量冲红票据
     */
    @Override
    public BatchOperationResult batchRefundReceipts(BatchRefundDTO refundDTO) {
        BatchOperationResult result = new BatchOperationResult();
        
        for (String receiptId : refundDTO.getReceiptIds()) {
            try {
                refundReceipt(receiptId, refundDTO.getRefundReason());
                result.addSuccess(receiptId);
            } catch (Exception e) {
                result.addFailed(receiptId, e.getMessage());
                log.error("批量冲红票据失败，receiptId: {}", receiptId, e);
            }
        }
        
        return result;
    }
}
```

#### 10. 前端增加电子票据管理界面和团检票据管理
**任务编号：** VS-010  
**预计工期：** 4天  
**负责人：** 前端开发工程师

**任务详情：**
- 在现有支付面板中集成票据功能
- 创建团检票据管理专用界面
- 实现票据查看、下载、重新申请等功能

**支付面板票据功能集成：**
```vue
<!-- FeePannel.vue 票据功能扩展 -->
<template>
  <div class="fee-pannel">
    <!-- 现有支付功能 -->
    
    <!-- 电子票据区域 -->
    <a-card title="电子票据" class="receipt-card">
      <div v-if="billInfo.electronicReceiptStatus">
        <a-descriptions :column="2" size="small">
          <a-descriptions-item label="票据号">
            {{ billInfo.electronicReceiptNo }}
          </a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-tag :color="getReceiptStatusColor(billInfo.electronicReceiptStatus)">
              {{ billInfo.electronicReceiptStatus }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="开票时间">
            {{ billInfo.receiptIssueTime }}
          </a-descriptions-item>
          <a-descriptions-item label="操作">
            <a-space>
              <a-button 
                type="link" 
                size="small" 
                @click="handleViewReceipt"
                v-if="billInfo.electronicReceiptUrl">
                查看
              </a-button>
              <a-button 
                type="link" 
                size="small" 
                @click="handleDownloadReceipt"
                v-if="billInfo.electronicReceiptUrl">
                下载
              </a-button>
              <a-button 
                type="link" 
                size="small" 
                @click="handleRefundReceipt"
                v-if="canRefundReceipt">
                冲红
              </a-button>
            </a-space>
          </a-descriptions-item>
        </a-descriptions>
      </div>
      
      <div v-else-if="canApplyReceipt">
        <a-result
          status="info"
          title="未申请电子票据"
          sub-title="支付完成后可申请电子票据">
          <template #extra>
            <a-button type="primary" @click="handleApplyReceipt">
              申请电子票据
            </a-button>
          </template>
        </a-result>
      </div>
      
      <div v-else>
        <a-empty description="暂无票据信息" />
      </div>
    </a-card>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { useMessage } from '/@/hooks/web/useMessage';
import { applyReceipt, downloadReceipt, refundReceipt } from '/@/api/invoice/electronicReceipt';

const { createMessage } = useMessage();

// 票据相关方法
const handleApplyReceipt = async () => {
  try {
    await applyReceipt(billInfo.value.id);
    createMessage.success('票据申请成功');
    // 刷新票据信息
    await refreshBillInfo();
  } catch (error) {
    createMessage.error('票据申请失败：' + error.message);
  }
};

const handleViewReceipt = () => {
  // 在新窗口打开票据
  window.open(billInfo.value.electronicReceiptUrl, '_blank');
};

const handleDownloadReceipt = async () => {
  try {
    const response = await downloadReceipt(billInfo.value.electronicReceiptId);
    // 处理文件下载
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', `电子票据_${billInfo.value.electronicReceiptNo}.pdf`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  } catch (error) {
    createMessage.error('票据下载失败：' + error.message);
  }
};

const handleRefundReceipt = () => {
  // 打开冲红对话框
  refundModalVisible.value = true;
};

const canApplyReceipt = computed(() => {
  return billInfo.value.status === '已支付' && 
         !billInfo.value.electronicReceiptId &&
         billInfo.value.amount > 0;
});

const canRefundReceipt = computed(() => {
  return billInfo.value.electronicReceiptStatus === '已开具';
});
</script>
```

**团检票据管理界面：**
```vue
<!-- CompanyReceiptManagement.vue -->
<template>
  <div class="company-receipt-management">
    <a-card title="团检票据管理">
      <!-- 团检基本信息 -->
      <div class="company-info">
        <a-descriptions :column="3">
          <a-descriptions-item label="单位名称">
            {{ companyReg.companyName }}
          </a-descriptions-item>
          <a-descriptions-item label="预约名称">
            {{ companyReg.regName }}
          </a-descriptions-item>
          <a-descriptions-item label="预约人数">
            {{ companyReg.personCount }}
          </a-descriptions-item>
        </a-descriptions>
      </div>
      
      <!-- 票据操作区域 -->
      <div class="receipt-actions">
        <a-space>
          <a-button 
            type="primary" 
            @click="handlePreIssueReceipt"
            v-if="canPreIssue">
            预开票据
          </a-button>
          <a-button 
            @click="handleViewReceipt"
            v-if="hasReceipt">
            查看票据
          </a-button>
          <a-button 
            @click="handleDownloadReceipt"
            v-if="hasReceipt">
            下载票据
          </a-button>
        </a-space>
      </div>
      
      <!-- 票据信息展示 -->
      <div class="receipt-info" v-if="receiptInfo">
        <a-descriptions title="票据信息" :column="2">
          <a-descriptions-item label="票据号">
            {{ receiptInfo.receiptNo }}
          </a-descriptions-item>
          <a-descriptions-item label="票据类型">
            <a-tag :color="getReceiptTypeColor(receiptInfo.receiptType)">
              {{ receiptInfo.receiptType }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="票据状态">
            <a-tag :color="getReceiptStatusColor(receiptInfo.receiptStatus)">
              {{ receiptInfo.receiptStatus }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="票据金额">
            ¥{{ receiptInfo.totalAmount?.toFixed(2) }}
          </a-descriptions-item>
          <a-descriptions-item label="开具时间">
            {{ receiptInfo.issueTime }}
          </a-descriptions-item>
        </a-descriptions>
      </div>
      
      <!-- 关联支付记录 -->
      <div class="payment-records">
        <a-table
          title="关联支付记录"
          :columns="paymentColumns"
          :data-source="paymentRecords"
          :pagination="false">
          
          <template #status="{ record }">
            <a-tag :color="getPaymentStatusColor(record.status)">
              {{ record.status }}
            </a-tag>
          </template>
          
          <template #action="{ record }">
            <a-button 
              type="link" 
              size="small"
              @click="handlePartialRefund(record)"
              v-if="canPartialRefund(record)">
              部分冲红
            </a-button>
          </template>
        </a-table>
      </div>
    </a-card>
  </div>
</template>
```

#### 11. 实现发票批量操作功能(批量申请、批量冲红、批量下载)
**任务编号：** VS-011  
**预计工期：** 3天  
**负责人：** 全栈开发工程师

#### 12. 实现电子票据审计报告和监控告警功能
**任务编号：** VS-012  
**预计工期：** 3天  
**负责人：** 后端开发工程师 + 系统管理员

**任务详情：**
- 实现批量申请票据功能
- 实现批量冲红处理
- 实现批量下载和打包功能
- 提供批量操作的进度反馈

**批量操作服务实现：**
```java
@Service
public class ElectronicReceiptBatchService {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @Autowired
    private TaskExecutor taskExecutor;
    
    /**
     * 批量申请电子票据（异步处理）
     */
    public String batchApplyReceiptsAsync(List<String> billIds, String operatorId) {
        String taskId = UUID.randomUUID().toString();
        
        // 创建批量任务记录
        BatchTaskRecord taskRecord = new BatchTaskRecord();
        taskRecord.setTaskId(taskId);
        taskRecord.setTaskType("批量申请票据");
        taskRecord.setTotalCount(billIds.size());
        taskRecord.setOperatorId(operatorId);
        taskRecord.setStatus("进行中");
        
        // 保存到Redis
        redisTemplate.opsForValue().set("batch_task:" + taskId, taskRecord, Duration.ofHours(24));
        
        // 异步执行批量操作
        taskExecutor.execute(() -> {
            executeBatchApply(taskId, billIds, taskRecord);
        });
        
        return taskId;
    }
    
    private void executeBatchApply(String taskId, List<String> billIds, BatchTaskRecord taskRecord) {
        int successCount = 0;
        int failCount = 0;
        List<BatchOperationDetail> details = new ArrayList<>();
        
        for (String billId : billIds) {
            try {
                ElectronicReceipt receipt = electronicReceiptService.applyElectronicReceipt(billId);
                successCount++;
                
                BatchOperationDetail detail = new BatchOperationDetail();
                detail.setBusinessId(billId);
                detail.setStatus("成功");
                detail.setResult("票据申请成功，票据ID：" + receipt.getId());
                details.add(detail);
                
            } catch (Exception e) {
                failCount++;
                
                BatchOperationDetail detail = new BatchOperationDetail();
                detail.setBusinessId(billId);
                detail.setStatus("失败");
                detail.setResult("申请失败：" + e.getMessage());
                details.add(detail);
                
                log.error("批量申请票据失败，billId: {}", billId, e);
            }
            
            // 更新进度
            taskRecord.setSuccessCount(successCount);
            taskRecord.setFailCount(failCount);
            taskRecord.setProgress((successCount + failCount) * 100 / billIds.size());
            taskRecord.setDetails(details);
            
            redisTemplate.opsForValue().set("batch_task:" + taskId, taskRecord, Duration.ofHours(24));
            
            // 避免过于频繁的操作
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
        
        // 标记任务完成
        taskRecord.setStatus("已完成");
        taskRecord.setEndTime(new Date());
        redisTemplate.opsForValue().set("batch_task:" + taskId, taskRecord, Duration.ofHours(24));
    }
    
    /**
     * 批量下载票据（生成ZIP文件）
     */
    public String batchDownloadReceipts(List<String> receiptIds) throws Exception {
        String zipFileName = "electronic_receipts_" + 
            DateUtil.format(new Date(), "yyyyMMdd_HHmmss") + ".zip";
        String zipFilePath = uploadPath + "/temp/" + zipFileName;
        
        // 创建临时目录
        File tempDir = new File(uploadPath + "/temp");
        if (!tempDir.exists()) {
            tempDir.mkdirs();
        }
        
        try (ZipOutputStream zos = new ZipOutputStream(new FileOutputStream(zipFilePath))) {
            for (String receiptId : receiptIds) {
                ElectronicReceipt receipt = electronicReceiptService.getById(receiptId);
                if (receipt != null && StringUtils.isNotBlank(receipt.getReceiptUrl())) {
                    try {
                        // 下载票据文件
                        byte[] receiptData = downloadReceiptFile(receipt.getReceiptUrl());
                        
                        // 添加到ZIP文件
                        String fileName = buildReceiptFileName(receipt);
                        ZipEntry entry = new ZipEntry(fileName);
                        zos.putNextEntry(entry);
                        zos.write(receiptData);
                        zos.closeEntry();
                        
                    } catch (Exception e) {
                        log.error("下载票据文件失败，receiptId: {}", receiptId, e);
                        
                        // 添加错误信息文件
                        String errorInfo = "票据下载失败：" + e.getMessage();
                        ZipEntry errorEntry = new ZipEntry(receipt.getReceiptNo() + "_error.txt");
                        zos.putNextEntry(errorEntry);
                        zos.write(errorInfo.getBytes(StandardCharsets.UTF_8));
                        zos.closeEntry();
                    }
                }
            }
        }
        
        return "/temp/" + zipFileName;
    }
    
    /**
     * 获取批量任务进度
     */
    public BatchTaskRecord getBatchTaskProgress(String taskId) {
        return (BatchTaskRecord) redisTemplate.opsForValue().get("batch_task:" + taskId);
    }
}
```

**任务详情：**
- 实现审计报告自动生成功能
- 建立实时监控告警机制
- 提供风险预警和异常检测
- 实现合规性监控和趋势分析

**审计报告服务实现：**
```java
@Service
public class ElectronicReceiptAuditReportService {
    
    /**
     * 生成定期审计报告
     */
    @Scheduled(cron = "0 0 6 * * ?") // 每天早上6点生成日报
    public void generateDailyAuditReport() {
        String reportDate = DateUtil.format(DateUtil.yesterday(), "yyyy-MM-dd");
        
        DailyAuditReport report = new DailyAuditReport();
        report.setReportDate(reportDate);
        
        // 1. 操作统计
        AuditOperationStats operationStats = getOperationStats(reportDate);
        report.setOperationStats(operationStats);
        
        // 2. 风险分析
        RiskAnalysisReport riskAnalysis = getRiskAnalysis(reportDate);
        report.setRiskAnalysis(riskAnalysis);
        
        // 3. 合规检查结果
        ComplianceCheckReport complianceReport = getComplianceReport(reportDate);
        report.setComplianceReport(complianceReport);
        
        // 4. 异常事件汇总
        List<AbnormalEvent> abnormalEvents = getAbnormalEvents(reportDate);
        report.setAbnormalEvents(abnormalEvents);
        
        // 5. 趋势分析
        TrendAnalysisReport trendAnalysis = getTrendAnalysis(reportDate, 7); // 7天趋势
        report.setTrendAnalysis(trendAnalysis);
        
        // 生成报告文件
        generateReportFile(report);
        
        // 发送报告邮件
        sendReportEmail(report);
    }
    
    /**
     * 生成月度审计报告
     */
    @Scheduled(cron = "0 0 7 1 * ?") // 每月1号早上7点生成月报
    public void generateMonthlyAuditReport() {
        String lastMonth = DateUtil.format(DateUtil.lastMonth(), "yyyy-MM");
        
        MonthlyAuditReport report = new MonthlyAuditReport();
        report.setReportMonth(lastMonth);
        
        // 月度汇总统计
        report.setMonthlySummary(getMonthlySummary(lastMonth));
        
        // 风险趋势分析
        report.setRiskTrend(getMonthlyRiskTrend(lastMonth));
        
        // 合规性分析
        report.setComplianceAnalysis(getMonthlyComplianceAnalysis(lastMonth));
        
        // 用户行为分析
        report.setUserBehaviorAnalysis(getUserBehaviorAnalysis(lastMonth));
        
        // 系统性能分析
        report.setSystemPerformance(getSystemPerformanceAnalysis(lastMonth));
        
        // 建议和改进措施
        report.setRecommendations(generateRecommendations(report));
        
        generateReportFile(report);
        sendReportEmail(report);
    }
    
    /**
     * 实时风险监控
     */
    @EventListener
    @Async
    public void handleHighRiskOperation(HighRiskOperationEvent event) {
        // 实时风险评估
        RiskAssessment assessment = assessRealTimeRisk(event);
        
        if (assessment.getRiskLevel() >= RiskLevel.HIGH) {
            // 创建告警
            AlertEvent alert = new AlertEvent();
            alert.setAlertType("HIGH_RISK_OPERATION");
            alert.setAlertLevel(assessment.getRiskLevel().name());
            alert.setAlertTitle("电子票据高风险操作告警");
            alert.setAlertContent(buildAlertContent(event, assessment));
            alert.setBusinessData(JSON.toJSONString(event));
            alert.setCreateTime(new Date());
            
            // 发送告警
            alertService.sendAlert(alert);
            
            // 如果是关键风险，立即通知管理员
            if (assessment.getRiskLevel() == RiskLevel.CRITICAL) {
                notificationService.sendUrgentNotification(alert);
            }
        }
    }
    
    /**
     * 合规性监控检查
     */
    @Scheduled(cron = "0 */15 * * * ?") // 每15分钟检查一次
    public void performComplianceMonitoring() {
        // 检查待处理的票据
        List<ElectronicReceipt> pendingReceipts = electronicReceiptService.getPendingReceipts();
        
        for (ElectronicReceipt receipt : pendingReceipts) {
            // 检查超时风险
            checkTimeoutRisk(receipt);
            
            // 检查状态异常
            checkStatusAbnormal(receipt);
            
            // 检查金额异常
            checkAmountAbnormal(receipt);
        }
        
        // 检查批量操作异常
        checkBatchOperationAbnormal();
        
        // 检查接口调用异常
        checkInterfaceCallAbnormal();
    }
    
    /**
     * 生成风险预警
     */
    public void generateRiskWarning(String riskType, Object riskData) {
        RiskWarning warning = new RiskWarning();
        warning.setRiskType(riskType);
        warning.setRiskLevel(assessRiskLevel(riskType, riskData));
        warning.setWarningTitle(buildWarningTitle(riskType));
        warning.setWarningContent(buildWarningContent(riskType, riskData));
        warning.setCreateTime(new Date());
        warning.setStatus("PENDING");
        
        // 保存预警记录
        riskWarningService.save(warning);
        
        // 发送预警通知
        if (warning.getRiskLevel() >= RiskLevel.MEDIUM) {
            alertService.sendRiskWarning(warning);
        }
    }
    
    /**
     * 系统健康度检查
     */
    @Scheduled(cron = "0 0 */2 * * ?") // 每2小时检查一次
    public void performSystemHealthCheck() {
        SystemHealthReport health = new SystemHealthReport();
        
        // 检查票据申请成功率
        double applySuccessRate = calculateApplySuccessRate();
        health.setApplySuccessRate(applySuccessRate);
        
        // 检查接口响应时间
        double avgResponseTime = calculateAvgResponseTime();
        health.setAvgResponseTime(avgResponseTime);
        
        // 检查错误率
        double errorRate = calculateErrorRate();
        health.setErrorRate(errorRate);
        
        // 检查数据一致性
        ConsistencyCheckResult consistency = checkDataConsistency();
        health.setConsistencyCheck(consistency);
        
        // 评估系统健康度
        SystemHealthLevel healthLevel = evaluateSystemHealth(health);
        health.setHealthLevel(healthLevel);
        
        // 如果健康度较低，发送告警
        if (healthLevel.ordinal() <= SystemHealthLevel.POOR.ordinal()) {
            AlertEvent alert = new AlertEvent();
            alert.setAlertType("SYSTEM_HEALTH");
            alert.setAlertLevel("HIGH");
            alert.setAlertTitle("电子票据系统健康度告警");
            alert.setAlertContent("系统健康度较低，请及时处理");
            alert.setBusinessData(JSON.toJSONString(health));
            
            alertService.sendAlert(alert);
        }
        
        // 记录健康度历史
        systemHealthHistoryService.recordHealth(health);
    }
}
```

**监控告警前端界面：**
```vue
<!-- ElectronicReceiptMonitoring.vue -->
<template>
  <div class="receipt-monitoring">
    <a-card title="电子票据监控中心">
      
      <!-- 系统健康度面板 -->
      <div class="health-panel">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-card class="health-card" :class="getHealthCardClass(systemHealth.healthLevel)">
              <a-statistic
                title="系统健康度"
                :value="getHealthScore(systemHealth.healthLevel)"
                suffix="%">
                <template #prefix>
                  <HeartOutlined :style="{ color: getHealthColor(systemHealth.healthLevel) }" />
                </template>
              </a-statistic>
            </a-card>
          </a-col>
          
          <a-col :span="6">
            <a-card class="metric-card">
              <a-statistic
                title="申请成功率"
                :value="systemHealth.applySuccessRate"
                suffix="%"
                :precision="2"
                :value-style="{ color: systemHealth.applySuccessRate > 95 ? '#52c41a' : '#f5222d' }">
              </a-statistic>
            </a-card>
          </a-col>
          
          <a-col :span="6">
            <a-card class="metric-card">
              <a-statistic
                title="平均响应时间"
                :value="systemHealth.avgResponseTime"
                suffix="ms"
                :precision="0"
                :value-style="{ color: systemHealth.avgResponseTime < 1000 ? '#52c41a' : '#fa8c16' }">
              </a-statistic>
            </a-card>
          </a-col>
          
          <a-col :span="6">
            <a-card class="metric-card">
              <a-statistic
                title="错误率"
                :value="systemHealth.errorRate"
                suffix="%"
                :precision="2"
                :value-style="{ color: systemHealth.errorRate < 1 ? '#52c41a' : '#f5222d' }">
              </a-statistic>
            </a-card>
          </a-col>
        </a-row>
      </div>
      
      <!-- 实时告警面板 -->
      <div class="alert-panel">
        <a-card title="实时告警" size="small">
          <template #extra>
            <a-badge :count="pendingAlertCount" :show-zero="false">
              <a-button size="small" @click="refreshAlerts">
                <template #icon><ReloadOutlined /></template>
                刷新
              </a-button>
            </a-badge>
          </template>
          
          <a-list
            :data-source="recentAlerts"
            :split="false"
            size="small">
            <template #renderItem="{ item }">
              <a-list-item>
                <a-list-item-meta>
                  <template #avatar>
                    <a-avatar :style="{ backgroundColor: getAlertLevelColor(item.alertLevel) }">
                      <template #icon>
                        <WarningOutlined v-if="item.alertLevel === 'HIGH'" />
                        <ExclamationCircleOutlined v-else-if="item.alertLevel === 'MEDIUM'" />
                        <InfoCircleOutlined v-else />
                      </template>
                    </a-avatar>
                  </template>
                  <template #title>
                    <span>{{ item.alertTitle }}</span>
                    <a-tag size="small" :color="getAlertLevelColor(item.alertLevel)" style="margin-left: 8px">
                      {{ getAlertLevelText(item.alertLevel) }}
                    </a-tag>
                  </template>
                  <template #description>
                    <div>{{ item.alertContent }}</div>
                    <div class="alert-time">{{ formatTime(item.createTime) }}</div>
                  </template>
                </a-list-item-meta>
                <template #actions>
                  <a @click="handleAlert(item)">处理</a>
                  <a @click="viewAlertDetail(item)">详情</a>
                </template>
              </a-list-item>
            </template>
          </a-list>
        </a-card>
      </div>
      
      <!-- 风险趋势图表 -->
      <div class="risk-trend-panel">
        <a-card title="风险趋势分析" size="small">
          <div ref="riskTrendChart" style="height: 300px;"></div>
        </a-card>
      </div>
      
      <!-- 合规性监控 -->
      <div class="compliance-panel">
        <a-card title="合规性监控" size="small">
          <a-row :gutter="16">
            <a-col :span="8">
              <div class="compliance-item">
                <a-progress
                  type="circle"
                  :percent="complianceStats.amountCompliance"
                  :width="80"
                  :stroke-color="getComplianceColor(complianceStats.amountCompliance)">
                </a-progress>
                <div class="compliance-label">金额合规率</div>
              </div>
            </a-col>
            
            <a-col :span="8">
              <div class="compliance-item">
                <a-progress
                  type="circle"
                  :percent="complianceStats.timeCompliance"
                  :width="80"
                  :stroke-color="getComplianceColor(complianceStats.timeCompliance)">
                </a-progress>
                <div class="compliance-label">时效合规率</div>
              </div>
            </a-col>
            
            <a-col :span="8">
              <div class="compliance-item">
                <a-progress
                  type="circle"
                  :percent="complianceStats.processCompliance"
                  :width="80"
                  :stroke-color="getComplianceColor(complianceStats.processCompliance)">
                </a-progress>
                <div class="compliance-label">流程合规率</div>
              </div>
            </a-col>
          </a-row>
        </a-card>
      </div>
      
      <!-- 操作日志实时流 -->
      <div class="operation-stream-panel">
        <a-card title="操作实时流" size="small">
          <template #extra>
            <a-switch v-model:checked="autoRefresh" checked-children="自动" un-checked-children="手动" />
          </template>
          
          <a-timeline mode="left" class="operation-timeline">
            <a-timeline-item
              v-for="operation in recentOperations"
              :key="operation.id"
              :color="getOperationColor(operation.riskLevel)">
              <template #label>{{ formatTime(operation.createTime) }}</template>
              
              <div class="operation-item">
                <div class="operation-header">
                  <span class="operation-type">{{ operation.auditType }}</span>
                  <a-tag size="small" :color="getRiskLevelColor(operation.riskLevel)">
                    {{ getRiskLevelText(operation.riskLevel) }}
                  </a-tag>
                </div>
                <div class="operation-content">{{ operation.operationDesc }}</div>
                <div class="operation-meta">
                  <span>操作人：{{ operation.operatorName }}</span>
                  <span style="margin-left: 16px">IP：{{ operation.clientIp }}</span>
                </div>
              </div>
            </a-timeline-item>
          </a-timeline>
        </a-card>
      </div>
    </a-card>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { useECharts } from '/@/hooks/web/useECharts';
import { 
  getSystemHealth, 
  getRecentAlerts, 
  getComplianceStats,
  getRecentOperations,
  getRiskTrendData
} from '/@/api/invoice/monitoring';

// 实时数据刷新
let refreshTimer = null;

onMounted(() => {
  loadData();
  
  // 设置自动刷新
  if (autoRefresh.value) {
    startAutoRefresh();
  }
  
  // 初始化风险趋势图表
  initRiskTrendChart();
});

onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer);
  }
});

const startAutoRefresh = () => {
  refreshTimer = setInterval(() => {
    loadData();
  }, 30000); // 30秒刷新一次
};

const initRiskTrendChart = () => {
  const chartDom = riskTrendChart.value;
  const { setOptions } = useECharts(chartDom);
  
  // 获取风险趋势数据并渲染图表
  getRiskTrendData().then(data => {
    const option = {
      title: { text: '风险趋势' },
      tooltip: { trigger: 'axis' },
      legend: { data: ['低风险', '中风险', '高风险', '关键风险'] },
      xAxis: { data: data.dates },
      yAxis: { type: 'value' },
      series: [
        { name: '低风险', type: 'line', data: data.lowRisk },
        { name: '中风险', type: 'line', data: data.mediumRisk },
        { name: '高风险', type: 'line', data: data.highRisk },
        { name: '关键风险', type: 'line', data: data.criticalRisk }
      ]
    };
    
    setOptions(option);
  });
};
</script>
```

### 🔹 低优先级任务

#### 12. 实现票据数据统计和团检财务报表功能
**任务编号：** VS-012  
**预计工期：** 3天  
**负责人：** 后端开发工程师

**任务详情：**
- 实现票据开具统计功能
- 创建团检财务报表
- 提供票据异常监控和分析

**统计服务实现：**
```java
@Service
public class ReceiptStatisticsService {
    
    /**
     * 获取票据统计数据
     */
    public ReceiptStatisticsVO getReceiptStatistics(String startDate, String endDate) {
        ReceiptStatisticsVO statistics = new ReceiptStatisticsVO();
        
        // 基础统计
        statistics.setTotalCount(getTotalReceiptCount(startDate, endDate));
        statistics.setTodayCount(getTodayReceiptCount());
        statistics.setPendingCount(getPendingReceiptCount());
        statistics.setErrorCount(getErrorReceiptCount());
        
        // 金额统计
        statistics.setTotalAmount(getTotalReceiptAmount(startDate, endDate));
        statistics.setMonthlyAmount(getMonthlyReceiptAmount());
        statistics.setRefundAmount(getRefundReceiptAmount(startDate, endDate));
        
        // 类型统计
        statistics.setTypeStatistics(getReceiptTypeStatistics(startDate, endDate));
        
        // 状态统计
        statistics.setStatusStatistics(getReceiptStatusStatistics(startDate, endDate));
        
        // 趋势数据
        statistics.setTrendData(getReceiptTrendData(startDate, endDate));
        
        return statistics;
    }
    
    /**
     * 获取团检财务报表
     */
    public CompanyReceiptReportVO getCompanyReceiptReport(String companyRegId) {
        CompanyReceiptReportVO report = new CompanyReceiptReportVO();
        
        // 团检基本信息
        CompanyReg companyReg = companyRegService.getById(companyRegId);
        report.setCompanyInfo(companyReg);
        
        // 预开票据信息
        ElectronicReceipt preIssueReceipt = electronicReceiptService.getByCompanyRegId(companyRegId);
        if (preIssueReceipt != null) {
            report.setPreIssueReceiptInfo(preIssueReceipt);
        }
        
        // 实际支付统计
        List<CustomerRegBill> bills = customerRegBillService.getByCompanyRegId(companyRegId);
        BigDecimal totalPaidAmount = bills.stream()
            .map(CustomerRegBill::getAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        report.setTotalPaidAmount(totalPaidAmount);
        
        // 支付明细
        report.setPaymentDetails(getCompanyPaymentDetails(companyRegId));
        
        // 票据关联情况
        report.setReceiptLinkStatus(getReceiptLinkStatus(companyRegId));
        
        return report;
    }
}
```

---

## 📊 项目管里信息

### 时间计划
- **项目总工期：** 30个工作日
- **高优先级任务：** 18个工作日
- **中优先级任务：** 9个工作日  
- **低优先级任务：** 3个工作日

### 人力资源
- **系统架构师：** 1人，负责整体设计和技术决策
- **后端开发工程师：** 2人，负责业务逻辑实现
- **前端开发工程师：** 1人，负责用户界面开发
- **测试工程师：** 1人，负责功能测试和集成测试
- **业务分析师：** 1人，负责需求分析和业务流程梳理

### 技术依赖
- **后端技术栈：** Spring Boot 2.7.18, MyBatis-Plus, Redis
- **前端技术栈：** Vue 3, Ant Design Vue, TypeScript
- **数据库：** MySQL 8.0+
- **接口对接：** HTTP REST API, 数字签名认证
- **文件处理：** PDF生成, ZIP打包, 二维码生成

### 风险评估
1. **接口对接风险：** vsaas接口规范理解偏差，需要充分的对接测试
2. **业务复杂度风险：** 团检预开票据场景复杂，需要仔细设计状态流转
3. **性能风险：** 批量操作可能影响系统性能，需要异步处理和限流
4. **数据一致性风险：** 票据状态同步可能存在延迟，需要容错机制

### 质量保证
- **代码评审：** 所有代码提交需要经过同行评审
- **单元测试：** 核心业务逻辑测试覆盖率不低于80%
- **集成测试：** 与vsaas接口的完整测试
- **用户验收测试：** 业务场景的端到端测试

### 交付标准
- **功能完整性：** 所有任务功能按需求实现
- **接口稳定性：** 与vsaas接口对接稳定，错误处理完善
- **用户体验：** 界面友好，操作流程清晰
- **系统性能：** 批量操作响应时间在可接受范围内
- **文档完整性：** 提供完整的技术文档和用户手册

---

## 📝 总结

本任务计划基于现有体检管理系统的技术架构，充分考虑了支付单与支付记录的一对多关系，以及团检预开票据的特殊业务场景。通过分阶段实施，先完成核心功能，再逐步完善管理界面和统计功能，确保项目能够稳步推进并满足业务需求。

统一的电子发票管理页面将成为整个系统的核心，提供集中化的票据管理能力，大大提升工作效率。批量操作功能的引入将进一步提升用户体验，满足大批量票据处理的需求。

整个实施方案既保持了与现有系统的良好兼容性，又为未来的功能扩展预留了充分的空间。