<template>
  <div>
    <!-- 查询区域 -->
    <div class="jeecg-basic-table-form-container">
      <a-form ref="formRef" @keyup.enter.native="searchQuery" :model="queryParam" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-row :gutter="24">
          <a-col :lg="8">
            <a-form-item label="客户端名称">
              <a-input placeholder="请输入客户端名称" v-model:value="queryParam.clientName"></a-input>
            </a-form-item>
          </a-col>
          <a-col :lg="8">
            <a-form-item label="状态">
              <j-dict-select-tag placeholder="请选择状态" v-model:value="queryParam.status" dictCode="api_client_status"/>
            </a-form-item>
          </a-col>
          <a-col :lg="8">
            <span style="float: left; overflow: hidden;" class="table-page-search-submitButtons">
              <a-col :lg="6">
                <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
                <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              </a-col>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="jeecg-basic-table-form-container">
      <div class="anty-form-btn">
        <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
        <a-button type="primary" icon="download" @click="handleExportXls('API客户端配置')">导出</a-button>
        <a-button type="primary" icon="upload" @click="handleImportExcel">导入</a-button>
        <a-dropdown v-if="selectedRowKeys.length > 0">
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="batchHandleDelete">
                <Icon icon="ant-design:delete-outlined"></Icon>
                删除
              </a-menu-item>
            </a-menu>
          </template>
          <a-button>批量操作
            <Icon icon="mdi:chevron-down"></Icon>
          </a-button>
        </a-dropdown>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="jeecg-basic-table-container">
      <a-table
        ref="tableRef"
        size="middle"
        :scroll="{x:true}"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        @change="handleTableChange">

        <template #htmlSlot="{text}">
          <div v-html="text"></div>
        </template>
        
        <template #pcaSlot="{text, record}">
          {{ record.provinceName + '/' + record.cityName + '/' + record.areaName }}
        </template>

        <template #statusSlot="{text}">
          <a-tag :color="text == 1 ? 'green' : 'red'">
            {{ text == 1 ? '启用' : '禁用' }}
          </a-tag>
        </template>

        <template #apiSecretSlot="{text}">
          <span v-if="text">{{ text.substring(0, 8) }}****{{ text.substring(text.length - 8) }}</span>
        </template>

        <template #action="{ record }">
          <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)"/>
        </template>
      </a-table>
    </div>

    <!-- 表单弹窗 -->
    <ApiClientModal ref="modalRef" @register="registerModal" @success="handleSuccess"></ApiClientModal>
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed, unref } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { useModal } from '/@/components/Modal';
  import { useListPage } from '/@/hooks/system/useListPage';
  import ApiClientModal from './modules/ApiClientModal.vue';
  import { columns, searchFormSchema } from './ApiClient.data';
  import { list, deleteOne, batchDelete, getImportUrl, getExportUrl, toggleStatus, regenerateKeys } from './ApiClient.api';
  import { downloadByOnlineUrl } from '/@/utils/common/renderUtils';

  const checkedKeys = ref<Array<string | number>>([]);
  const [registerModal, { openModal }] = useModal();
  const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
    tableProps: {
      title: 'API客户端配置',
      api: list,
      columns,
      canResize: false,
      formConfig: {
        labelWidth: 120,
        schemas: searchFormSchema,
        autoSubmitOnEnter: true,
      },
      actionColumn: {
        width: 120,
        fixed: 'right',
      },
      beforeFetch: (params) => {
        return Object.assign({ column: 'createTime', order: 'desc' }, params);
      },
    },
    exportConfig: {
      name: 'API客户端配置',
      url: getExportUrl,
    },
    importConfig: {
      url: getImportUrl,
    },
  });

  const [registerTable, { reload }] = tableContext;

  // 表格操作
  function handleAdd() {
    openModal(true, {
      isUpdate: false,
    });
  }

  function handleEdit(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
    });
  }

  function handleDelete(record: Recordable) {
    deleteOne({ id: record.id }, handleSuccess);
  }

  function batchHandleDelete() {
    batchDelete({ ids: checkedKeys.value }, handleSuccess);
  }

  function handleSuccess() {
    (unref(registerTable) as any)?.reload();
  }

  function handleToggleStatus(record: Recordable) {
    const status = record.status === 1 ? 0 : 1;
    toggleStatus({ id: record.id, status }).then(() => {
      handleSuccess();
    });
  }

  function handleRegenerateKeys(record: Recordable) {
    regenerateKeys({ id: record.id }).then(() => {
      handleSuccess();
    });
  }

  function getTableAction(record): any {
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
      },
    ];
  }

  function getDropDownAction(record): any {
    return [
      {
        label: record.status === 1 ? '禁用' : '启用',
        popConfirm: {
          title: `确定要${record.status === 1 ? '禁用' : '启用'}吗？`,
          confirm: handleToggleStatus.bind(null, record),
        },
      },
      {
        label: '重新生成密钥',
        popConfirm: {
          title: '确定要重新生成API密钥吗？原密钥将失效！',
          confirm: handleRegenerateKeys.bind(null, record),
        },
      },
      {
        label: '删除',
        color: 'error',
        popConfirm: {
          title: '是否确认删除',
          confirm: handleDelete.bind(null, record),
        },
      },
    ];
  }
</script>

<style scoped>
.jeecg-basic-table-form-container {
  padding: 20px;
  background: #fff;
  border-radius: 4px;
  margin-bottom: 16px;
}

.anty-form-btn {
  text-align: center;
  margin-top: 16px;
}

.jeecg-basic-table-container {
  padding: 0 20px 20px 20px;
  background: #fff;
  border-radius: 4px;
}
</style>
